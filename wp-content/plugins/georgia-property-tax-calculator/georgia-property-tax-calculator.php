<?php
/**
 * Plugin Name: Georgia Property Tax Calculator
 * Plugin URI: https://propertytaxreportsusa.com
 * Description: Calculate estimated Georgia property taxes with Google Maps API for county lookup, RentCast API for home values, and email notifications.
 * Version: 1.0.0
 * Author: Property Tax Reports USA
 * Author URI: https://propertytaxreportsusa.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: georgia-property-tax-calculator
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('GPTC_VERSION', '1.0.0');
define('GPTC_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('GPTC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('GPTC_PLUGIN_FILE', __FILE__);
define('GPTC_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main Plugin Class
 */
class GeorgiaPropertyTaxCalculator {
    
    /**
     * Single instance of the plugin
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('GeorgiaPropertyTaxCalculator', 'uninstall'));
        
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once GPTC_PLUGIN_DIR . 'includes/class-gptc-database.php';
        require_once GPTC_PLUGIN_DIR . 'includes/class-gptc-admin.php';
        require_once GPTC_PLUGIN_DIR . 'includes/class-gptc-shortcode.php';
        require_once GPTC_PLUGIN_DIR . 'includes/class-gptc-ajax.php';
        require_once GPTC_PLUGIN_DIR . 'includes/class-gptc-email.php';
        require_once GPTC_PLUGIN_DIR . 'includes/class-gptc-api.php';
        require_once GPTC_PLUGIN_DIR . 'includes/class-gptc-calculator.php';
        require_once GPTC_PLUGIN_DIR . 'includes/class-gptc-county-data.php';
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain
        load_plugin_textdomain('georgia-property-tax-calculator', false, dirname(GPTC_PLUGIN_BASENAME) . '/languages');
        
        // Initialize classes
        GPTC_Admin::get_instance();
        GPTC_Shortcode::get_instance();
        GPTC_Ajax::get_instance();
        GPTC_County_Data::get_instance();
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_scripts() {
        wp_enqueue_style(
            'gptc-frontend-style',
            GPTC_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            GPTC_VERSION
        );
        
        wp_enqueue_script(
            'gptc-frontend-script',
            GPTC_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            GPTC_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('gptc-frontend-script', 'gptc_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('gptc_nonce'),
            'loading_text' => __('Calculating...', 'georgia-property-tax-calculator'),
            'error_text' => __('An error occurred. Please try again.', 'georgia-property-tax-calculator')
        ));
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts($hook) {
        if (strpos($hook, 'gptc-settings') === false) {
            return;
        }
        
        wp_enqueue_style(
            'gptc-admin-style',
            GPTC_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            GPTC_VERSION
        );
        
        wp_enqueue_script(
            'gptc-admin-script',
            GPTC_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            GPTC_VERSION,
            true
        );
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        GPTC_Database::create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove database tables
        GPTC_Database::drop_tables();
        
        // Remove options
        delete_option('gptc_settings');
        delete_option('gptc_google_maps_api_key');
        delete_option('gptc_rentcast_api_key');
        delete_option('gptc_business_email');
        delete_option('gptc_company_logo');
    }
    
    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $default_settings = array(
            'business_email' => '<EMAIL>',
            'company_phone' => '************',
            'company_name' => 'Property Tax Reports USA',
            'assessment_ratio' => 40,
            'default_millage_rate' => 32.5,
            'default_exemption' => 2000
        );
        
        add_option('gptc_settings', $default_settings);
    }
}

// Initialize the plugin
function gptc_init() {
    return GeorgiaPropertyTaxCalculator::get_instance();
}

// Start the plugin
add_action('plugins_loaded', 'gptc_init');

/**
 * Helper function to get plugin settings
 */
function gptc_get_settings() {
    return get_option('gptc_settings', array());
}

/**
 * Helper function to get specific setting
 */
function gptc_get_setting($key, $default = '') {
    $settings = gptc_get_settings();
    return isset($settings[$key]) ? $settings[$key] : $default;
}
