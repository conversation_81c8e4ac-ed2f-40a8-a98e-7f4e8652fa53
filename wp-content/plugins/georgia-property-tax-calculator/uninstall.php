<?php
/**
 * Uninstall script for Georgia Property Tax Calculator
 * 
 * This file is executed when the plugin is deleted from WordPress admin.
 * It removes all plugin data including database tables and options.
 */

// Prevent direct access
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Include the database class
require_once plugin_dir_path(__FILE__) . 'includes/class-gptc-database.php';

/**
 * Remove all plugin data
 */
function gptc_uninstall_plugin() {
    global $wpdb;
    
    // Remove database tables
    $tables = array(
        $wpdb->prefix . 'gptc_submissions',
        $wpdb->prefix . 'gptc_counties',
        $wpdb->prefix . 'gptc_api_logs'
    );
    
    foreach ($tables as $table) {
        $wpdb->query("DROP TABLE IF EXISTS $table");
    }
    
    // Remove plugin options
    $options = array(
        'gptc_settings',
        'gptc_google_maps_api_key',
        'gptc_rentcast_api_key',
        'gptc_business_email',
        'gptc_company_phone',
        'gptc_company_logo',
        'gptc_company_name',
        'gptc_version',
        'gptc_db_version'
    );
    
    foreach ($options as $option) {
        delete_option($option);
        delete_site_option($option); // For multisite
    }
    
    // Remove user meta (if any)
    $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'gptc_%'");
    
    // Remove transients
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_gptc_%'");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_gptc_%'");
    
    // Clear any cached data
    wp_cache_flush();
    
    // Log uninstall (optional)
    error_log('Georgia Property Tax Calculator plugin uninstalled successfully');
}

// Execute uninstall
gptc_uninstall_plugin();
