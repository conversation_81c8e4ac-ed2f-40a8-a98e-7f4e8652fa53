=== Georgia Property Tax Calculator ===
Contributors: propertytaxreportsusa
Tags: property tax, georgia, calculator, real estate, tax estimation
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Calculate estimated Georgia property taxes with automatic county detection, property value estimation, and email notifications.

== Description ==

The Georgia Property Tax Calculator is a comprehensive WordPress plugin that provides accurate property tax estimates for all 159 Georgia counties. Perfect for real estate professionals, property owners, and tax consultants.

= Key Features =

* **Accurate Tax Calculations** - Uses Georgia's 40% assessment ratio and current county millage rates
* **Automatic County Detection** - Google Maps API integration for address-based county lookup
* **Property Value Estimation** - RentCast API integration for real-time property valuations
* **Email Notifications** - Branded emails sent to users and business
* **Complete County Database** - All 159 Georgia counties with current tax data
* **Multiple Exemptions** - Support for homestead, senior, and disabled exemptions
* **Responsive Design** - Mobile-friendly calculator form
* **Secure Admin Interface** - Safe API key management and configuration

= How It Works =

1. User enters name, email, and property address
2. Plugin automatically detects county from address
3. Estimates property value using market data
4. Calculates tax using Georgia's standard formula
5. Displays detailed breakdown with annual/monthly estimates
6. Sends branded email notifications

= Perfect For =

* Real estate agents and brokers
* Property tax consultants
* Mortgage professionals
* Property management companies
* Government websites
* Real estate websites

= Shortcode =

Display the calculator anywhere using: `[gtp_tax_form]`

= API Integration =

* **Google Maps API** - For accurate address geocoding and county detection
* **RentCast API** - For real-time property value estimation
* Both APIs have free tiers available

== Installation ==

1. Upload the plugin files to `/wp-content/plugins/georgia-property-tax-calculator/`
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Go to Settings → Property Tax Estimator to configure
4. Add your API keys for full functionality
5. Use the shortcode `[gtp_tax_form]` on any page or post

== Frequently Asked Questions ==

= Do I need API keys to use this plugin? =

The plugin works with basic functionality without API keys, but for best results:
- Google Maps API key enables automatic county detection
- RentCast API key provides accurate property value estimates

= How accurate are the tax calculations? =

The plugin uses Georgia's official 40% assessment ratio and current county millage rates. However, actual taxes may vary due to local assessments, special districts, or other factors.

= Can I customize the calculator appearance? =

Yes! The plugin includes CSS classes for easy customization. You can override styles in your theme.

= Does it work with all Georgia counties? =

Yes! The plugin includes data for all 159 Georgia counties with current millage rates and exemptions.

= Are the emails customizable? =

Yes, the emails include your company logo, contact information, and can be customized through WordPress hooks.

= Is the plugin secure? =

Absolutely. The plugin includes:
- Nonce verification for all forms
- Input sanitization and validation
- Secure API key storage
- Capability checks for admin functions

== Screenshots ==

1. Calculator form with user-friendly interface
2. Detailed tax calculation results
3. Admin settings page for API configuration
4. Submissions management dashboard
5. County data management interface

== Changelog ==

= 1.0.0 =
* Initial release
* Complete Georgia property tax calculation engine
* Google Maps API integration for county detection
* RentCast API integration for property values
* Email notification system with branded templates
* Secure admin interface with API key management
* Complete database of all 159 Georgia counties
* Responsive calculator form with validation
* Comprehensive error handling and logging
* Multiple exemption types support

== Upgrade Notice ==

= 1.0.0 =
Initial release of the Georgia Property Tax Calculator plugin.

== API Requirements ==

= Google Maps API =
* **Service**: Geocoding API
* **Purpose**: Convert addresses to counties
* **Cost**: Free tier available (up to 40,000 requests/month)
* **Setup**: https://console.cloud.google.com

= RentCast API =
* **Service**: Automated Valuation Model (AVM)
* **Purpose**: Property value estimation
* **Cost**: Free tier available (50-100 requests/day)
* **Setup**: https://www.rentcast.io

== Technical Details ==

= System Requirements =
* WordPress 5.0 or higher
* PHP 7.4 or higher
* MySQL 5.6 or higher

= Database Tables =
* `wp_gptc_submissions` - Form submissions and calculations
* `wp_gptc_counties` - Georgia county data
* `wp_gptc_api_logs` - API request logging

= Hooks Available =
* `gptc_calculation_result` - Modify calculation results
* `gptc_user_email_content` - Customize user emails
* `gptc_business_email_content` - Customize business emails
* `gptc_exemptions` - Add custom exemptions

== Support ==

For support, documentation, and feature requests:

* **Website**: https://propertytaxreportsusa.com
* **Email**: <EMAIL>
* **Phone**: ************

== Privacy Policy ==

This plugin collects and stores:
* User-submitted form data (name, email, address)
* Calculated tax information
* API request logs for debugging

All data is stored securely in your WordPress database and is not shared with third parties except for the API services used (Google Maps and RentCast) as necessary for functionality.

== Credits ==

Developed by Property Tax Reports USA
* Specializing in Georgia property tax consulting
* Professional tax assessment and appeal services
* Expert knowledge of Georgia tax law and procedures
