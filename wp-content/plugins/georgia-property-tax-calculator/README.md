# Georgia Property Tax Calculator

A comprehensive WordPress plugin for calculating estimated Georgia property taxes with Google Maps API integration for county lookup, RentCast API for property values, and automated email notifications.

## Features

- **Property Tax Calculation**: Accurate Georgia property tax estimates using 40% assessment ratio and county-specific millage rates
- **Google Maps Integration**: Automatic county detection from property addresses
- **RentCast API Integration**: Real-time property value estimation
- **Email Notifications**: Branded emails sent to users and business
- **Secure Admin Interface**: Safe API key management and configuration
- **Responsive Design**: Mobile-friendly calculator form
- **Comprehensive Database**: All 159 Georgia counties with current tax data
- **Multiple Exemptions**: Homestead, senior, and disabled exemptions support

## Installation

### Automatic Installation
1. Log in to your WordPress Admin
2. Go to **Plugins → Add New → Upload Plugin**
3. Upload the `georgia-property-tax-calculator.zip` file
4. Click **Install Now**
5. Activate the plugin

### Manual Installation
1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through the WordPress admin

## Configuration

### 1. Basic Setup
After activation, go to **Settings → Property Tax Estimator** to configure:

- **Business Email**: Where form submissions will be sent (default: <EMAIL>)
- **Company Phone**: Phone number for email notifications (default: ************)
- **Company Logo**: URL to your company logo for branded emails

### 2. API Configuration
For full functionality, configure these APIs:

#### Google Maps API
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select existing
3. Enable **Geocoding API**
4. Create API credentials
5. Enter the API key in plugin settings

#### RentCast API
1. Visit [RentCast.io](https://www.rentcast.io)
2. Sign up for an account (free tier available)
3. Generate API key from dashboard
4. Enter the API key in plugin settings

### 3. Test Configuration
Use the built-in API testing tools in the admin to verify your setup.

## Usage

### Shortcode
Display the calculator on any page or post using:

```
[gtp_tax_form]
```

### Shortcode Parameters
- `title`: Custom title (default: "Georgia Property Tax Calculator")
- `show_title`: Show/hide title ("yes" or "no", default: "yes")
- `theme`: Visual theme ("default")
- `show_scenarios`: Show tax scenarios ("yes" or "no", default: "no")

### Examples
```
[gtp_tax_form title="Calculate Your Property Tax" show_scenarios="yes"]
```

## How It Works

1. **User Input**: User enters name, email, and property address
2. **Address Geocoding**: Google Maps API determines county from address
3. **Property Valuation**: RentCast API estimates property value (or user can provide)
4. **Tax Calculation**: Plugin calculates tax using Georgia's 40% assessment ratio and county millage rates
5. **Results Display**: Shows detailed breakdown with annual and monthly estimates
6. **Email Notifications**: Sends branded emails to user and business

## Tax Calculation Method

The plugin uses Georgia's standard property tax calculation:

1. **Market Value**: From RentCast API or user input
2. **Assessment Ratio**: 40% for residential properties
3. **Assessed Value**: Market Value × 40%
4. **Exemptions**: Homestead ($2,000), Senior, Disabled
5. **Taxable Value**: Assessed Value - Exemptions
6. **Annual Tax**: (Taxable Value ÷ 1,000) × County Millage Rate

## Database Tables

The plugin creates three tables:

- `wp_gptc_submissions`: Form submissions and calculations
- `wp_gptc_counties`: Georgia county data with millage rates
- `wp_gptc_api_logs`: API request logging for debugging

## Admin Features

### Submissions Management
View all form submissions at **Tools → Tax Calculator Submissions**:
- User information and contact details
- Property details and calculated taxes
- Submission timestamps and status

### County Data Management
- Pre-loaded with all 159 Georgia counties
- Editable millage rates and exemptions
- Import/export functionality for bulk updates

### API Monitoring
- Request logging and error tracking
- Usage statistics and performance metrics
- Built-in API testing tools

## Security Features

- **Nonce Verification**: All forms protected against CSRF attacks
- **Input Sanitization**: All user input properly sanitized
- **Capability Checks**: Admin functions restricted to authorized users
- **Secure API Storage**: API keys encrypted in WordPress database
- **Rate Limiting**: Built-in protection against abuse

## Customization

### Styling
Override default styles by adding CSS to your theme:

```css
.gptc-calculator-container {
    /* Your custom styles */
}
```

### Hooks and Filters
Available for developers:

```php
// Modify calculation results
add_filter('gptc_calculation_result', 'your_function');

// Customize email content
add_filter('gptc_user_email_content', 'your_function');

// Add custom exemptions
add_filter('gptc_exemptions', 'your_function');
```

## Troubleshooting

### Common Issues

**Calculator not displaying**
- Check if shortcode is correct: `[gtp_tax_form]`
- Verify plugin is activated
- Check for JavaScript errors in browser console

**API errors**
- Verify API keys are entered correctly
- Test APIs using admin testing tools
- Check API quotas and billing

**Email not sending**
- Verify WordPress email configuration
- Check spam folders
- Test with admin email testing tool

**Incorrect calculations**
- Verify county data is up to date
- Check millage rates in admin
- Ensure property values are reasonable

### Debug Mode
Enable WordPress debug mode to see detailed error messages:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Support

For support and questions:
- **Email**: <EMAIL>
- **Phone**: ************

## Changelog

### Version 1.0.0
- Initial release
- Complete Georgia property tax calculation
- Google Maps and RentCast API integration
- Email notification system
- Admin interface and settings
- All 159 Georgia counties included

## License

This plugin is licensed under the GPL v2 or later.

## Credits

Developed by Property Tax Reports USA
- Website: https://propertytaxreportsusa.com
- Email: <EMAIL>
- Phone: ************
