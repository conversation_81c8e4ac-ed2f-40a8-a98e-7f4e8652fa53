/**
 * Georgia Property Tax Calculator - Frontend JavaScript
 */

(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        initializeCalculator();
    });
    
    /**
     * Initialize the calculator functionality
     */
    function initializeCalculator() {
        // Form validation
        setupFormValidation();
        
        // Auto-formatting
        setupAutoFormatting();
        
        // County auto-complete
        setupCountyAutocomplete();
        
        // Property value estimation
        setupPropertyValueEstimation();
        
        // Results handling
        setupResultsHandling();
    }
    
    /**
     * Setup form validation
     */
    function setupFormValidation() {
        $('#gptc-tax-form').on('submit', function(e) {
            e.preventDefault();
            
            var isValid = validateForm();
            if (isValid) {
                submitForm();
            }
        });
        
        // Real-time validation
        $('#gptc_name').on('blur', function() {
            validateName($(this));
        });
        
        $('#gptc_email').on('blur', function() {
            validateEmail($(this));
        });
        
        $('#gptc_address').on('blur', function() {
            validateAddress($(this));
        });
        
        $('#gptc_property_value').on('blur', function() {
            validatePropertyValue($(this));
        });
    }
    
    /**
     * Validate the entire form
     */
    function validateForm() {
        var isValid = true;
        
        // Clear previous errors
        $('.gptc-field-error').remove();
        $('.gptc-input, .gptc-select').removeClass('gptc-error');
        
        // Validate name
        if (!validateName($('#gptc_name'))) {
            isValid = false;
        }
        
        // Validate email
        if (!validateEmail($('#gptc_email'))) {
            isValid = false;
        }
        
        // Validate address
        if (!validateAddress($('#gptc_address'))) {
            isValid = false;
        }
        
        // Validate property value (if provided)
        if ($('#gptc_property_value').val() && !validatePropertyValue($('#gptc_property_value'))) {
            isValid = false;
        }
        
        return isValid;
    }
    
    /**
     * Validate name field
     */
    function validateName($field) {
        var value = $field.val().trim();
        var isValid = true;
        
        if (value.length === 0) {
            showFieldError($field, 'Name is required.');
            isValid = false;
        } else if (value.length < 2) {
            showFieldError($field, 'Name must be at least 2 characters long.');
            isValid = false;
        } else if (!/^[a-zA-Z\s\-'\.]+$/.test(value)) {
            showFieldError($field, 'Name contains invalid characters.');
            isValid = false;
        }
        
        if (isValid) {
            clearFieldError($field);
        }
        
        return isValid;
    }
    
    /**
     * Validate email field
     */
    function validateEmail($field) {
        var value = $field.val().trim();
        var isValid = true;
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (value.length === 0) {
            showFieldError($field, 'Email address is required.');
            isValid = false;
        } else if (!emailRegex.test(value)) {
            showFieldError($field, 'Please enter a valid email address.');
            isValid = false;
        }
        
        if (isValid) {
            clearFieldError($field);
        }
        
        return isValid;
    }
    
    /**
     * Validate address field
     */
    function validateAddress($field) {
        var value = $field.val().trim();
        var isValid = true;
        
        if (value.length === 0) {
            showFieldError($field, 'Property address is required.');
            isValid = false;
        } else if (value.length < 10) {
            showFieldError($field, 'Please enter a complete address.');
            isValid = false;
        } else if (!/\d/.test(value)) {
            showFieldError($field, 'Address should include a street number.');
            isValid = false;
        }
        
        if (isValid) {
            clearFieldError($field);
        }
        
        return isValid;
    }
    
    /**
     * Validate property value field
     */
    function validatePropertyValue($field) {
        var value = parseFloat($field.val());
        var isValid = true;
        
        if (isNaN(value)) {
            showFieldError($field, 'Please enter a valid number.');
            isValid = false;
        } else if (value < 10000) {
            showFieldError($field, 'Property value seems too low. Please verify.');
            isValid = false;
        } else if (value > 50000000) {
            showFieldError($field, 'Property value seems too high. Please verify.');
            isValid = false;
        }
        
        if (isValid) {
            clearFieldError($field);
        }
        
        return isValid;
    }
    
    /**
     * Show field error
     */
    function showFieldError($field, message) {
        $field.addClass('gptc-error');
        
        var $error = $('<div class="gptc-field-error">' + message + '</div>');
        $error.css({
            'color': '#e74c3c',
            'font-size': '12px',
            'margin-top': '5px',
            'display': 'block'
        });
        
        $field.closest('.gptc-form-group').append($error);
    }
    
    /**
     * Clear field error
     */
    function clearFieldError($field) {
        $field.removeClass('gptc-error');
        $field.closest('.gptc-form-group').find('.gptc-field-error').remove();
    }
    
    /**
     * Setup auto-formatting
     */
    function setupAutoFormatting() {
        // Format property value with commas
        $('#gptc_property_value').on('input', function() {
            var value = $(this).val().replace(/[^\d]/g, '');
            if (value) {
                var formatted = parseInt(value).toLocaleString();
                $(this).val(formatted);
            }
        });
        
        // Remove formatting on focus for easier editing
        $('#gptc_property_value').on('focus', function() {
            var value = $(this).val().replace(/[^\d]/g, '');
            $(this).val(value);
        });
        
        // Capitalize name
        $('#gptc_name').on('input', function() {
            var value = $(this).val();
            var capitalized = value.replace(/\b\w/g, function(l) {
                return l.toUpperCase();
            });
            $(this).val(capitalized);
        });
    }
    
    /**
     * Setup county autocomplete
     */
    function setupCountyAutocomplete() {
        $('#gptc_county').on('change', function() {
            var selectedCounty = $(this).val();
            if (selectedCounty) {
                // Could add county-specific information or validation here
                console.log('Selected county:', selectedCounty);
            }
        });
    }
    
    /**
     * Setup property value estimation
     */
    function setupPropertyValueEstimation() {
        var estimationTimeout;
        
        $('#gptc_address').on('input', function() {
            clearTimeout(estimationTimeout);
            
            estimationTimeout = setTimeout(function() {
                var address = $('#gptc_address').val().trim();
                if (address.length > 20 && !$('#gptc_property_value').val()) {
                    showEstimationHint();
                }
            }, 1000);
        });
    }
    
    /**
     * Show property value estimation hint
     */
    function showEstimationHint() {
        var $hint = $('#gptc_property_value').siblings('.gptc-estimation-hint');
        
        if ($hint.length === 0) {
            $hint = $('<small class="gptc-estimation-hint" style="color: #2c5aa0; font-style: italic;">We\'ll estimate the property value automatically if you leave this blank.</small>');
            $('#gptc_property_value').closest('.gptc-form-group').append($hint);
        }
        
        $hint.fadeIn();
    }
    
    /**
     * Setup results handling
     */
    function setupResultsHandling() {
        // Print functionality
        $(document).on('click', '.gptc-print-btn', function() {
            window.print();
        });
        
        // Share functionality
        $(document).on('click', '.gptc-share-btn', function() {
            if (navigator.share) {
                navigator.share({
                    title: 'Georgia Property Tax Estimate',
                    text: 'Check out my property tax estimate',
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                var url = window.location.href;
                navigator.clipboard.writeText(url).then(function() {
                    alert('Link copied to clipboard!');
                });
            }
        });
        
        // Save results functionality
        $(document).on('click', '.gptc-save-btn', function() {
            var resultsHtml = $('#gptc-results').html();
            var blob = new Blob([resultsHtml], { type: 'text/html' });
            var url = URL.createObjectURL(blob);
            
            var a = document.createElement('a');
            a.href = url;
            a.download = 'property-tax-estimate.html';
            a.click();
            
            URL.revokeObjectURL(url);
        });
    }
    
    /**
     * Submit the form via AJAX
     */
    function submitForm() {
        var $form = $('#gptc-tax-form');
        var $submitBtn = $('#gptc-submit-btn');
        var $results = $('#gptc-results');
        var $error = $('#gptc-error');
        
        // Show loading state
        $submitBtn.find('.gptc-btn-text').hide();
        $submitBtn.find('.gptc-btn-loading').show();
        $submitBtn.prop('disabled', true);
        
        // Hide previous results/errors
        $results.hide();
        $error.hide();
        
        // Prepare form data
        var formData = {
            action: 'gptc_calculate_tax',
            nonce: $form.find('[name="gptc_nonce"]').val(),
            name: $form.find('[name="gptc_name"]').val(),
            email: $form.find('[name="gptc_email"]').val(),
            address: $form.find('[name="gptc_address"]').val(),
            county: $form.find('[name="gptc_county"]').val(),
            property_value: $form.find('[name="gptc_property_value"]').val().replace(/[^\d]/g, ''),
            homestead: $form.find('[name="gptc_homestead"]').is(':checked') ? 1 : 0,
            senior: $form.find('[name="gptc_senior"]').is(':checked') ? 1 : 0,
            disabled: $form.find('[name="gptc_disabled"]').is(':checked') ? 1 : 0,
            show_scenarios: $form.data('show-scenarios') || 'no'
        };
        
        // Submit via AJAX
        $.ajax({
            url: gptc_ajax.ajax_url,
            type: 'POST',
            data: formData,
            dataType: 'json',
            timeout: 30000, // 30 second timeout
            success: function(response) {
                if (response.success) {
                    $results.html(response.data.html).fadeIn();
                    
                    // Show scenarios if available
                    if (response.data.scenarios_html) {
                        $('#gptc-scenarios').html(response.data.scenarios_html).fadeIn();
                    }
                    
                    // Scroll to results
                    $('html, body').animate({
                        scrollTop: $results.offset().top - 50
                    }, 500);
                    
                    // Track successful calculation
                    trackCalculation('success', formData);
                    
                } else {
                    var errorMessage = response.data && response.data.message ? 
                        response.data.message : 'An error occurred. Please try again.';
                    $error.html('<div class="gptc-error-message">' + errorMessage + '</div>').fadeIn();
                    
                    // Track failed calculation
                    trackCalculation('error', formData, errorMessage);
                }
            },
            error: function(xhr, status, error) {
                var errorMessage = 'An error occurred. Please check your internet connection and try again.';
                
                if (status === 'timeout') {
                    errorMessage = 'The request timed out. Please try again.';
                }
                
                $error.html('<div class="gptc-error-message">' + errorMessage + '</div>').fadeIn();
                
                // Track AJAX error
                trackCalculation('ajax_error', formData, error);
            },
            complete: function() {
                // Reset button state
                $submitBtn.find('.gptc-btn-loading').hide();
                $submitBtn.find('.gptc-btn-text').show();
                $submitBtn.prop('disabled', false);
            }
        });
    }
    
    /**
     * Track calculation events (for analytics)
     */
    function trackCalculation(event, data, error) {
        // Google Analytics tracking (if available)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'property_tax_calculation', {
                'event_category': 'Calculator',
                'event_label': event,
                'custom_map': {
                    'county': data.county || 'auto-detect',
                    'has_property_value': data.property_value ? 'yes' : 'no'
                }
            });
        }
        
        // Facebook Pixel tracking (if available)
        if (typeof fbq !== 'undefined') {
            fbq('track', 'Lead', {
                content_name: 'Property Tax Calculator',
                status: event
            });
        }
        
        // Console logging for debugging
        console.log('GPTC Calculation Event:', {
            event: event,
            data: data,
            error: error,
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * Utility function to format currency
     */
    function formatCurrency(amount) {
        return '$' + parseFloat(amount).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
    
    /**
     * Utility function to format numbers
     */
    function formatNumber(number) {
        return parseFloat(number).toLocaleString('en-US');
    }
    
    /**
     * Add CSS for error states
     */
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .gptc-input.gptc-error,
            .gptc-select.gptc-error {
                border-color: #e74c3c !important;
                box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1) !important;
            }
            
            .gptc-field-error {
                animation: gptc-error-shake 0.5s ease-in-out;
            }
            
            @keyframes gptc-error-shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
        `)
        .appendTo('head');

})(jQuery);
