/**
 * Georgia Property Tax Calculator - Admin JavaScript
 */

(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        initializeAdmin();
    });
    
    /**
     * Initialize admin functionality
     */
    function initializeAdmin() {
        setupApiKeyToggle();
        setupApiTesting();
        setupEmailTesting();
        setupFormValidation();
        setupTabSwitching();
        setupCountyManagement();
        setupImportExport();
    }
    
    /**
     * Setup API key show/hide toggle
     */
    function setupApiKeyToggle() {
        $(document).on('click', '.gptc-toggle-key-btn', function() {
            var $button = $(this);
            var $input = $button.siblings('input');
            
            if ($input.attr('type') === 'password') {
                $input.attr('type', 'text');
                $button.text('Hide Key');
            } else {
                $input.attr('type', 'password');
                $button.text('Show Key');
            }
        });
    }
    
    /**
     * Setup API testing functionality
     */
    function setupApiTesting() {
        $('#test-google-maps').on('click', function() {
            testApiConnection('google_maps', $(this));
        });
        
        $('#test-rentcast').on('click', function() {
            testApiConnection('rentcast', $(this));
        });
    }
    
    /**
     * Test API connection
     */
    function testApiConnection(apiType, $button) {
        var originalText = $button.text();
        var $results = $('#api-test-results');
        
        // Show loading state
        $button.html('<span class="gptc-spinner-admin"></span> Testing...').prop('disabled', true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'gptc_test_api',
                api_type: apiType,
                nonce: $('#gptc-admin-nonce').val()
            },
            success: function(response) {
                if (response.success) {
                    $results.html('<div class="gptc-message gptc-message-success">' + response.data.message + '</div>');
                } else {
                    $results.html('<div class="gptc-message gptc-message-error">' + response.data.message + '</div>');
                }
            },
            error: function() {
                $results.html('<div class="gptc-message gptc-message-error">Test failed. Please try again.</div>');
            },
            complete: function() {
                $button.text(originalText).prop('disabled', false);
                
                // Auto-hide results after 10 seconds
                setTimeout(function() {
                    $results.fadeOut();
                }, 10000);
            }
        });
    }
    
    /**
     * Setup email testing
     */
    function setupEmailTesting() {
        $('#test-email-btn').on('click', function() {
            var $button = $(this);
            var $emailInput = $('#test-email-address');
            var email = $emailInput.val();
            
            if (!email || !isValidEmail(email)) {
                alert('Please enter a valid email address.');
                $emailInput.focus();
                return;
            }
            
            var originalText = $button.text();
            $button.html('<span class="gptc-spinner-admin"></span> Sending...').prop('disabled', true);
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'gptc_send_test_email',
                    email: email,
                    nonce: $('#gptc-admin-nonce').val()
                },
                success: function(response) {
                    if (response.success) {
                        alert('Test email sent successfully!');
                    } else {
                        alert('Failed to send test email: ' + response.data.message);
                    }
                },
                error: function() {
                    alert('Failed to send test email. Please try again.');
                },
                complete: function() {
                    $button.text(originalText).prop('disabled', false);
                }
            });
        });
    }
    
    /**
     * Setup form validation
     */
    function setupFormValidation() {
        // Validate API keys
        $('input[name="gptc_google_maps_api_key"], input[name="gptc_rentcast_api_key"]').on('blur', function() {
            var $input = $(this);
            var value = $input.val().trim();
            
            if (value && value.length < 10) {
                showFieldWarning($input, 'API key seems too short. Please verify.');
            } else {
                clearFieldWarning($input);
            }
        });
        
        // Validate email
        $('input[name="gptc_business_email"]').on('blur', function() {
            var $input = $(this);
            var value = $input.val().trim();
            
            if (value && !isValidEmail(value)) {
                showFieldWarning($input, 'Please enter a valid email address.');
            } else {
                clearFieldWarning($input);
            }
        });
        
        // Validate phone
        $('input[name="gptc_company_phone"]').on('blur', function() {
            var $input = $(this);
            var value = $input.val().trim();
            
            if (value && !isValidPhone(value)) {
                showFieldWarning($input, 'Please enter a valid phone number.');
            } else {
                clearFieldWarning($input);
            }
        });
        
        // Validate URL
        $('input[name="gptc_company_logo"]').on('blur', function() {
            var $input = $(this);
            var value = $input.val().trim();
            
            if (value && !isValidUrl(value)) {
                showFieldWarning($input, 'Please enter a valid URL.');
            } else {
                clearFieldWarning($input);
            }
        });
    }
    
    /**
     * Show field warning
     */
    function showFieldWarning($field, message) {
        clearFieldWarning($field);
        
        var $warning = $('<div class="gptc-field-warning" style="color: #856404; font-size: 12px; margin-top: 5px;">' + message + '</div>');
        $field.closest('td').append($warning);
        $field.css('border-color', '#ffc107');
    }
    
    /**
     * Clear field warning
     */
    function clearFieldWarning($field) {
        $field.closest('td').find('.gptc-field-warning').remove();
        $field.css('border-color', '');
    }
    
    /**
     * Setup tab switching
     */
    function setupTabSwitching() {
        $('.gptc-tab').on('click', function(e) {
            e.preventDefault();
            
            var $tab = $(this);
            var target = $tab.data('target');
            
            // Update active tab
            $('.gptc-tab').removeClass('active');
            $tab.addClass('active');
            
            // Show target content
            $('.gptc-tab-content').removeClass('active').hide();
            $(target).addClass('active').show();
        });
    }
    
    /**
     * Setup county management
     */
    function setupCountyManagement() {
        // Edit county
        $(document).on('click', '.gptc-edit-county', function() {
            var countyId = $(this).data('county-id');
            openCountyEditModal(countyId);
        });
        
        // Delete county
        $(document).on('click', '.gptc-delete-county', function() {
            var countyId = $(this).data('county-id');
            var countyName = $(this).data('county-name');
            
            if (confirm('Are you sure you want to delete ' + countyName + ' County? This action cannot be undone.')) {
                deleteCounty(countyId);
            }
        });
        
        // Add new county
        $('#add-county-btn').on('click', function() {
            openCountyEditModal(0);
        });
    }
    
    /**
     * Open county edit modal
     */
    function openCountyEditModal(countyId) {
        // This would open a modal for editing county data
        // Implementation depends on your modal system
        console.log('Opening county edit modal for ID:', countyId);
    }
    
    /**
     * Delete county
     */
    function deleteCounty(countyId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'gptc_delete_county',
                county_id: countyId,
                nonce: $('#gptc-admin-nonce').val()
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Failed to delete county: ' + response.data.message);
                }
            },
            error: function() {
                alert('Failed to delete county. Please try again.');
            }
        });
    }
    
    /**
     * Setup import/export functionality
     */
    function setupImportExport() {
        // Import counties
        $('#import-counties-btn').on('click', function() {
            var $button = $(this);
            var $fileInput = $('#counties-import-file');
            var file = $fileInput[0].files[0];
            
            if (!file) {
                alert('Please select a CSV file to import.');
                return;
            }
            
            if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
                alert('Please select a valid CSV file.');
                return;
            }
            
            var formData = new FormData();
            formData.append('action', 'gptc_import_counties');
            formData.append('file', file);
            formData.append('nonce', $('#gptc-admin-nonce').val());
            
            var originalText = $button.text();
            $button.html('<span class="gptc-spinner-admin"></span> Importing...').prop('disabled', true);
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        alert('Counties imported successfully!');
                        location.reload();
                    } else {
                        alert('Import failed: ' + response.data.message);
                    }
                },
                error: function() {
                    alert('Import failed. Please try again.');
                },
                complete: function() {
                    $button.text(originalText).prop('disabled', false);
                }
            });
        });
        
        // Export counties
        $('#export-counties-btn').on('click', function() {
            var $button = $(this);
            var originalText = $button.text();
            
            $button.html('<span class="gptc-spinner-admin"></span> Exporting...').prop('disabled', true);
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'gptc_export_counties',
                    nonce: $('#gptc-admin-nonce').val()
                },
                success: function(response) {
                    if (response.success) {
                        // Create download link
                        var link = document.createElement('a');
                        link.href = response.data.url;
                        link.download = response.data.filename;
                        link.click();
                    } else {
                        alert('Export failed: ' + response.data.message);
                    }
                },
                error: function() {
                    alert('Export failed. Please try again.');
                },
                complete: function() {
                    $button.text(originalText).prop('disabled', false);
                }
            });
        });
    }
    
    /**
     * Validation helper functions
     */
    function isValidEmail(email) {
        var regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    }
    
    function isValidPhone(phone) {
        var regex = /^[\+]?[1-9][\d]{0,15}$/;
        var cleaned = phone.replace(/[\s\-\(\)\.]/g, '');
        return regex.test(cleaned) && cleaned.length >= 10;
    }
    
    function isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch (e) {
            return false;
        }
    }
    
    /**
     * Auto-save functionality
     */
    function setupAutoSave() {
        var saveTimeout;
        
        $('input, select, textarea').on('change', function() {
            clearTimeout(saveTimeout);
            
            saveTimeout = setTimeout(function() {
                // Auto-save settings
                saveSettings(true);
            }, 2000);
        });
    }
    
    /**
     * Save settings
     */
    function saveSettings(isAutoSave) {
        var $form = $('#gptc-settings-form');
        
        if (!isAutoSave) {
            // Show loading indicator for manual save
            $('#submit').val('Saving...').prop('disabled', true);
        }
        
        $.ajax({
            url: $form.attr('action'),
            type: 'POST',
            data: $form.serialize(),
            success: function(response) {
                if (isAutoSave) {
                    // Show subtle auto-save indicator
                    showAutoSaveIndicator();
                } else {
                    // Show success message for manual save
                    showSaveSuccess();
                }
            },
            error: function() {
                if (!isAutoSave) {
                    alert('Failed to save settings. Please try again.');
                }
            },
            complete: function() {
                if (!isAutoSave) {
                    $('#submit').val('Save Settings').prop('disabled', false);
                }
            }
        });
    }
    
    /**
     * Show auto-save indicator
     */
    function showAutoSaveIndicator() {
        var $indicator = $('#auto-save-indicator');
        
        if ($indicator.length === 0) {
            $indicator = $('<div id="auto-save-indicator" style="position: fixed; top: 32px; right: 20px; background: #00a32a; color: white; padding: 5px 10px; border-radius: 3px; font-size: 12px; z-index: 9999;">Settings saved</div>');
            $('body').append($indicator);
        }
        
        $indicator.fadeIn().delay(2000).fadeOut();
    }
    
    /**
     * Show save success message
     */
    function showSaveSuccess() {
        var $message = $('<div class="notice notice-success is-dismissible"><p>Settings saved successfully!</p></div>');
        $('.wrap h1').after($message);
        
        setTimeout(function() {
            $message.fadeOut();
        }, 5000);
    }
    
    // Initialize auto-save if enabled
    if (typeof gptcAdminSettings !== 'undefined' && gptcAdminSettings.autoSave) {
        setupAutoSave();
    }

})(jQuery);
