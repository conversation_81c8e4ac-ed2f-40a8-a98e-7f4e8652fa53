/* Georgia Property Tax Calculator - Admin Styles */

.gptc-admin-header {
    background: #ffffff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.gptc-admin-header h2 {
    margin: 0 0 10px 0;
    color: #2c5aa0;
    font-size: 24px;
}

.gptc-admin-header p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Settings Form */
.gptc-settings-section {
    background: #ffffff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin-bottom: 20px;
}

.gptc-settings-section h3 {
    margin: 0;
    padding: 15px 20px;
    background: #f6f7f7;
    border-bottom: 1px solid #c3c4c7;
    color: #2c5aa0;
    font-size: 18px;
}

.gptc-settings-content {
    padding: 20px;
}

/* API Key Fields */
.gptc-api-key-field {
    position: relative;
}

.gptc-api-key-field input[type="password"],
.gptc-api-key-field input[type="text"] {
    padding-right: 120px;
}

.gptc-toggle-key-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: #f0f0f1;
    border: 1px solid #c3c4c7;
    border-radius: 3px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
    color: #2271b1;
}

.gptc-toggle-key-btn:hover {
    background: #e0e0e1;
}

/* Test Section */
.gptc-test-section {
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.gptc-test-section h3 {
    margin: 0 0 10px 0;
    color: #2c5aa0;
    font-size: 18px;
}

.gptc-test-section p {
    margin: 0 0 15px 0;
    color: #666;
}

.gptc-test-btn {
    background: #2271b1;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 10px;
    margin-bottom: 10px;
    font-size: 13px;
}

.gptc-test-btn:hover {
    background: #135e96;
}

.gptc-test-btn:disabled {
    background: #c3c4c7;
    cursor: not-allowed;
}

#api-test-results {
    margin-top: 15px;
}

#api-test-results .notice {
    margin: 10px 0;
    padding: 10px 15px;
    border-radius: 3px;
}

/* Usage Stats */
.gptc-usage-stats {
    background: #ffffff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin-top: 20px;
}

.gptc-usage-stats h3 {
    margin: 0;
    padding: 15px 20px;
    background: #f6f7f7;
    border-bottom: 1px solid #c3c4c7;
    color: #2c5aa0;
    font-size: 18px;
}

.gptc-usage-stats .form-table {
    margin: 0;
}

.gptc-usage-stats .form-table th {
    background: #f9f9f9;
    font-weight: 600;
    color: #333;
}

.gptc-usage-stats .form-table td {
    font-size: 16px;
    font-weight: 600;
    color: #2c5aa0;
}

/* Submissions Table */
.gptc-submissions-table {
    background: #ffffff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.gptc-submissions-table .wp-list-table {
    border: none;
}

.gptc-submissions-table .wp-list-table th {
    background: #f6f7f7;
    border-bottom: 1px solid #c3c4c7;
    font-weight: 600;
    color: #333;
}

.gptc-submissions-table .wp-list-table td {
    border-bottom: 1px solid #f0f0f1;
    vertical-align: middle;
}

.gptc-submissions-table .wp-list-table tr:hover {
    background: #f6f7f7;
}

/* Status Indicators */
.status-completed {
    color: #00a32a;
    font-weight: 600;
}

.status-pending {
    color: #dba617;
    font-weight: 600;
}

.status-failed {
    color: #d63638;
    font-weight: 600;
}

/* County Management */
.gptc-county-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.gptc-county-card {
    background: #ffffff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 15px;
}

.gptc-county-card h4 {
    margin: 0 0 10px 0;
    color: #2c5aa0;
    font-size: 16px;
}

.gptc-county-detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 13px;
}

.gptc-county-detail-label {
    color: #666;
}

.gptc-county-detail-value {
    font-weight: 600;
    color: #333;
}

/* Import/Export */
.gptc-import-export {
    background: #ffffff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.gptc-import-export h3 {
    margin: 0 0 15px 0;
    color: #2c5aa0;
    font-size: 18px;
}

.gptc-file-input {
    margin-bottom: 10px;
}

.gptc-import-btn,
.gptc-export-btn {
    background: #2271b1;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 10px;
    font-size: 13px;
}

.gptc-import-btn:hover,
.gptc-export-btn:hover {
    background: #135e96;
}

/* Responsive Admin */
@media (max-width: 768px) {
    .gptc-county-grid {
        grid-template-columns: 1fr;
    }
    
    .gptc-api-key-field input[type="password"],
    .gptc-api-key-field input[type="text"] {
        padding-right: 10px;
        margin-bottom: 10px;
    }
    
    .gptc-toggle-key-btn {
        position: static;
        transform: none;
        display: block;
        width: 100%;
        margin-top: 5px;
    }
    
    .gptc-test-btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
}

/* Loading States */
.gptc-loading {
    opacity: 0.6;
    pointer-events: none;
}

.gptc-spinner-admin {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #c3c4c7;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: gptc-spin-admin 1s linear infinite;
    margin-right: 5px;
}

@keyframes gptc-spin-admin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.gptc-message {
    padding: 10px 15px;
    border-radius: 3px;
    margin: 10px 0;
}

.gptc-message-success {
    background: #d1e7dd;
    color: #0f5132;
    border: 1px solid #badbcc;
}

.gptc-message-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.gptc-message-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.gptc-message-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Help Text */
.gptc-help {
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 15px;
    margin: 15px 0;
    font-size: 13px;
    color: #666;
}

.gptc-help h4 {
    margin: 0 0 10px 0;
    color: #2c5aa0;
    font-size: 14px;
}

.gptc-help ul {
    margin: 10px 0;
    padding-left: 20px;
}

.gptc-help li {
    margin-bottom: 5px;
}

/* Tabs */
.gptc-tabs {
    border-bottom: 1px solid #c3c4c7;
    margin-bottom: 20px;
}

.gptc-tab {
    display: inline-block;
    padding: 10px 20px;
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-bottom: none;
    margin-right: 5px;
    cursor: pointer;
    color: #666;
    text-decoration: none;
}

.gptc-tab:hover {
    background: #e0e0e1;
    color: #333;
}

.gptc-tab.active {
    background: #ffffff;
    color: #2c5aa0;
    font-weight: 600;
}

.gptc-tab-content {
    display: none;
}

.gptc-tab-content.active {
    display: block;
}
