/* Georgia Property Tax Calculator - Frontend Styles */

.gptc-calculator-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.gptc-calculator-title {
    text-align: center;
    color: #2c5aa0;
    margin-bottom: 10px;
    font-size: 28px;
    font-weight: 600;
}

.gptc-calculator-description {
    text-align: center;
    margin-bottom: 30px;
    color: #666;
    font-size: 16px;
    line-height: 1.5;
}

/* Form Styles */
.gptc-tax-form {
    margin-bottom: 30px;
}

.gptc-form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.gptc-form-group {
    margin-bottom: 20px;
}

.gptc-form-group-half {
    flex: 1;
}

.gptc-form-group-third {
    flex: 1;
}

.gptc-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.gptc-required {
    color: #e74c3c;
}

.gptc-input,
.gptc-select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.gptc-input:focus,
.gptc-select:focus {
    outline: none;
    border-color: #2c5aa0;
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.gptc-help-text {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #666;
    font-style: italic;
}

/* Exemptions Section */
.gptc-exemptions-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    margin: 25px 0;
}

.gptc-section-title {
    margin: 0 0 15px 0;
    color: #2c5aa0;
    font-size: 18px;
    font-weight: 600;
}

.gptc-checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.gptc-checkbox-label input[type="checkbox"] {
    margin-right: 10px;
    margin-top: 2px;
    transform: scale(1.2);
}

.gptc-checkmark {
    margin-left: 5px;
}

/* Form Actions */
.gptc-form-actions {
    text-align: center;
    margin: 30px 0;
}

.gptc-submit-btn {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 18px;
    font-weight: 600;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
    position: relative;
}

.gptc-submit-btn:hover {
    background: linear-gradient(135deg, #1e3f73 0%, #2c5aa0 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
}

.gptc-submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.gptc-btn-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.gptc-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: gptc-spin 1s linear infinite;
}

@keyframes gptc-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Disclaimer */
.gptc-disclaimer {
    text-align: center;
    margin-top: 20px;
    padding: 15px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    font-size: 12px;
    color: #856404;
}

/* Results Styles */
.gptc-results {
    margin-top: 30px;
    animation: gptc-fadeIn 0.5s ease-in;
}

@keyframes gptc-fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.gptc-results-container {
    background: #ffffff;
    border: 2px solid #2c5aa0;
    border-radius: 8px;
    overflow: hidden;
}

.gptc-results-header {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.gptc-results-title {
    margin: 0 0 5px 0;
    font-size: 24px;
    font-weight: 600;
}

.gptc-results-county {
    font-size: 16px;
    opacity: 0.9;
}

.gptc-results-main {
    display: flex;
    padding: 30px 20px;
    background: #f8f9fa;
    justify-content: space-around;
    text-align: center;
}

.gptc-result-highlight {
    flex: 1;
}

.gptc-result-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.gptc-result-value {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 10px;
}

.gptc-result-primary {
    color: #2c5aa0;
}

.gptc-result-secondary {
    color: #27ae60;
}

/* Breakdown Styles */
.gptc-results-breakdown {
    padding: 20px;
}

.gptc-breakdown-title {
    margin: 0 0 15px 0;
    color: #2c5aa0;
    font-size: 18px;
    font-weight: 600;
    border-bottom: 2px solid #e1e5e9;
    padding-bottom: 10px;
}

.gptc-breakdown-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #e1e5e9;
}

.gptc-breakdown-label {
    color: #666;
    font-weight: 500;
}

.gptc-breakdown-value {
    font-weight: 600;
    color: #333;
}

/* Property Info */
.gptc-property-info {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #e1e5e9;
}

.gptc-property-title {
    margin: 0 0 15px 0;
    color: #2c5aa0;
    font-size: 16px;
    font-weight: 600;
}

.gptc-property-row {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    font-size: 14px;
}

.gptc-property-label {
    color: #666;
    font-weight: 500;
}

.gptc-property-value {
    color: #333;
}

/* Results Actions */
.gptc-results-actions {
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
    border-top: 1px solid #e1e5e9;
}

.gptc-btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 0 10px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gptc-btn-primary {
    background: #2c5aa0;
    color: white;
}

.gptc-btn-primary:hover {
    background: #1e3f73;
    transform: translateY(-1px);
}

.gptc-btn-secondary {
    background: #6c757d;
    color: white;
}

.gptc-btn-secondary:hover {
    background: #545b62;
    transform: translateY(-1px);
}

.gptc-results-disclaimer {
    padding: 15px 20px;
    background: #fff3cd;
    border-top: 1px solid #ffeaa7;
    font-size: 12px;
    color: #856404;
}

/* Scenarios Styles */
.gptc-scenarios {
    margin-top: 30px;
    animation: gptc-fadeIn 0.5s ease-in;
}

.gptc-scenarios-container {
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
}

.gptc-scenarios-title {
    margin: 0 0 10px 0;
    color: #2c5aa0;
    font-size: 22px;
    font-weight: 600;
}

.gptc-scenarios-description {
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
}

.gptc-scenarios-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.gptc-scenario-card {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.gptc-scenario-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.gptc-scenario-title {
    margin: 0 0 10px 0;
    color: #2c5aa0;
    font-size: 14px;
    font-weight: 600;
}

.gptc-scenario-tax {
    margin-bottom: 5px;
}

.gptc-scenario-amount {
    font-size: 20px;
    font-weight: 700;
    color: #2c5aa0;
}

.gptc-scenario-period {
    font-size: 12px;
    color: #666;
}

.gptc-scenario-monthly {
    font-size: 14px;
    color: #27ae60;
    margin-bottom: 10px;
}

.gptc-scenario-exemptions {
    font-size: 12px;
    color: #666;
}

/* Error Styles */
.gptc-error {
    margin-top: 20px;
    animation: gptc-fadeIn 0.5s ease-in;
}

.gptc-error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .gptc-calculator-container {
        margin: 10px;
        padding: 15px;
    }
    
    .gptc-form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .gptc-results-main {
        flex-direction: column;
        gap: 20px;
    }
    
    .gptc-scenarios-grid {
        grid-template-columns: 1fr;
    }
    
    .gptc-breakdown-row {
        flex-direction: column;
        gap: 5px;
    }
    
    .gptc-breakdown-value {
        text-align: right;
    }
    
    .gptc-btn {
        display: block;
        margin: 10px 0;
    }
}

@media (max-width: 480px) {
    .gptc-calculator-title {
        font-size: 22px;
    }
    
    .gptc-result-value {
        font-size: 24px;
    }
    
    .gptc-submit-btn {
        width: 100%;
        padding: 12px;
        font-size: 16px;
    }
}
