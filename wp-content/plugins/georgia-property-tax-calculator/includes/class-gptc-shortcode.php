<?php
/**
 * Shortcode functionality for Georgia Property Tax Calculator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GPTC_Shortcode {
    
    /**
     * Single instance
     */
    private static $instance = null;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_shortcode('gtp_tax_form', array($this, 'render_tax_form'));
    }
    
    /**
     * Render the tax calculator form
     */
    public function render_tax_form($atts) {
        $atts = shortcode_atts(array(
            'title' => __('Georgia Property Tax Calculator', 'georgia-property-tax-calculator'),
            'show_title' => 'yes',
            'theme' => 'default',
            'show_scenarios' => 'no'
        ), $atts, 'gtp_tax_form');
        
        ob_start();
        
        ?>
        <div class="gptc-calculator-container" data-theme="<?php echo esc_attr($atts['theme']); ?>">
            <?php if ($atts['show_title'] === 'yes'): ?>
                <h3 class="gptc-calculator-title"><?php echo esc_html($atts['title']); ?></h3>
            <?php endif; ?>
            
            <div class="gptc-calculator-description">
                <p><?php _e('Get an estimated property tax calculation for your Georgia property. Enter your information below to receive a detailed breakdown.', 'georgia-property-tax-calculator'); ?></p>
            </div>
            
            <form id="gptc-tax-form" class="gptc-tax-form" method="post">
                <?php wp_nonce_field('gptc_calculate_tax', 'gptc_nonce'); ?>
                
                <div class="gptc-form-row">
                    <div class="gptc-form-group gptc-form-group-half">
                        <label for="gptc_name" class="gptc-label">
                            <?php _e('Full Name', 'georgia-property-tax-calculator'); ?> <span class="gptc-required">*</span>
                        </label>
                        <input type="text" id="gptc_name" name="gptc_name" class="gptc-input" required>
                    </div>
                    
                    <div class="gptc-form-group gptc-form-group-half">
                        <label for="gptc_email" class="gptc-label">
                            <?php _e('Email Address', 'georgia-property-tax-calculator'); ?> <span class="gptc-required">*</span>
                        </label>
                        <input type="email" id="gptc_email" name="gptc_email" class="gptc-input" required>
                    </div>
                </div>
                
                <div class="gptc-form-group">
                    <label for="gptc_address" class="gptc-label">
                        <?php _e('Property Address', 'georgia-property-tax-calculator'); ?> <span class="gptc-required">*</span>
                    </label>
                    <input type="text" id="gptc_address" name="gptc_address" class="gptc-input" 
                           placeholder="<?php esc_attr_e('Enter full property address in Georgia', 'georgia-property-tax-calculator'); ?>" required>
                    <small class="gptc-help-text">
                        <?php _e('Include street address, city, and ZIP code for best results', 'georgia-property-tax-calculator'); ?>
                    </small>
                </div>
                
                <div class="gptc-form-row">
                    <div class="gptc-form-group gptc-form-group-half">
                        <label for="gptc_county" class="gptc-label">
                            <?php _e('County (Optional)', 'georgia-property-tax-calculator'); ?>
                        </label>
                        <select id="gptc_county" name="gptc_county" class="gptc-select">
                            <option value=""><?php _e('Auto-detect from address', 'georgia-property-tax-calculator'); ?></option>
                            <?php echo $this->get_county_options(); ?>
                        </select>
                    </div>
                    
                    <div class="gptc-form-group gptc-form-group-half">
                        <label for="gptc_property_value" class="gptc-label">
                            <?php _e('Property Value (Optional)', 'georgia-property-tax-calculator'); ?>
                        </label>
                        <input type="number" id="gptc_property_value" name="gptc_property_value" 
                               class="gptc-input" min="10000" max="50000000" step="1000"
                               placeholder="<?php esc_attr_e('Auto-estimate from address', 'georgia-property-tax-calculator'); ?>">
                    </div>
                </div>
                
                <div class="gptc-exemptions-section">
                    <h4 class="gptc-section-title"><?php _e('Exemptions (Optional)', 'georgia-property-tax-calculator'); ?></h4>
                    <div class="gptc-form-row">
                        <div class="gptc-form-group gptc-form-group-third">
                            <label class="gptc-checkbox-label">
                                <input type="checkbox" id="gptc_homestead" name="gptc_homestead" value="1" checked>
                                <span class="gptc-checkmark"></span>
                                <?php _e('Homestead Exemption', 'georgia-property-tax-calculator'); ?>
                            </label>
                            <small class="gptc-help-text"><?php _e('$2,000 standard exemption', 'georgia-property-tax-calculator'); ?></small>
                        </div>
                        
                        <div class="gptc-form-group gptc-form-group-third">
                            <label class="gptc-checkbox-label">
                                <input type="checkbox" id="gptc_senior" name="gptc_senior" value="1">
                                <span class="gptc-checkmark"></span>
                                <?php _e('Senior Exemption', 'georgia-property-tax-calculator'); ?>
                            </label>
                            <small class="gptc-help-text"><?php _e('Age 65+ additional exemption', 'georgia-property-tax-calculator'); ?></small>
                        </div>
                        
                        <div class="gptc-form-group gptc-form-group-third">
                            <label class="gptc-checkbox-label">
                                <input type="checkbox" id="gptc_disabled" name="gptc_disabled" value="1">
                                <span class="gptc-checkmark"></span>
                                <?php _e('Disabled Exemption', 'georgia-property-tax-calculator'); ?>
                            </label>
                            <small class="gptc-help-text"><?php _e('Disability exemption if applicable', 'georgia-property-tax-calculator'); ?></small>
                        </div>
                    </div>
                </div>
                
                <div class="gptc-form-actions">
                    <button type="submit" class="gptc-submit-btn" id="gptc-submit-btn">
                        <span class="gptc-btn-text"><?php _e('Calculate Property Tax', 'georgia-property-tax-calculator'); ?></span>
                        <span class="gptc-btn-loading" style="display: none;">
                            <span class="gptc-spinner"></span>
                            <?php _e('Calculating...', 'georgia-property-tax-calculator'); ?>
                        </span>
                    </button>
                </div>
                
                <div class="gptc-disclaimer">
                    <small>
                        <?php _e('* This is an estimate only. Actual property taxes may vary based on local assessments, exemptions, and other factors. For official tax information, contact your county tax assessor.', 'georgia-property-tax-calculator'); ?>
                    </small>
                </div>
            </form>
            
            <div id="gptc-results" class="gptc-results" style="display: none;">
                <!-- Results will be populated via AJAX -->
            </div>
            
            <div id="gptc-error" class="gptc-error" style="display: none;">
                <!-- Error messages will be displayed here -->
            </div>
        </div>
        
        <?php if ($atts['show_scenarios'] === 'yes'): ?>
            <div id="gptc-scenarios" class="gptc-scenarios" style="display: none;">
                <!-- Tax scenarios will be displayed here -->
            </div>
        <?php endif; ?>
        
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('#gptc-tax-form').on('submit', function(e) {
                e.preventDefault();
                
                var $form = $(this);
                var $submitBtn = $('#gptc-submit-btn');
                var $results = $('#gptc-results');
                var $error = $('#gptc-error');
                
                // Show loading state
                $submitBtn.find('.gptc-btn-text').hide();
                $submitBtn.find('.gptc-btn-loading').show();
                $submitBtn.prop('disabled', true);
                
                // Hide previous results/errors
                $results.hide();
                $error.hide();
                
                // Prepare form data
                var formData = {
                    action: 'gptc_calculate_tax',
                    nonce: $form.find('[name="gptc_nonce"]').val(),
                    name: $form.find('[name="gptc_name"]').val(),
                    email: $form.find('[name="gptc_email"]').val(),
                    address: $form.find('[name="gptc_address"]').val(),
                    county: $form.find('[name="gptc_county"]').val(),
                    property_value: $form.find('[name="gptc_property_value"]').val(),
                    homestead: $form.find('[name="gptc_homestead"]').is(':checked') ? 1 : 0,
                    senior: $form.find('[name="gptc_senior"]').is(':checked') ? 1 : 0,
                    disabled: $form.find('[name="gptc_disabled"]').is(':checked') ? 1 : 0,
                    show_scenarios: '<?php echo esc_js($atts['show_scenarios']); ?>'
                };
                
                // Submit via AJAX
                $.ajax({
                    url: gptc_ajax.ajax_url,
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            $results.html(response.data.html).fadeIn();
                            
                            // Show scenarios if enabled
                            if (response.data.scenarios_html) {
                                $('#gptc-scenarios').html(response.data.scenarios_html).fadeIn();
                            }
                            
                            // Scroll to results
                            $('html, body').animate({
                                scrollTop: $results.offset().top - 50
                            }, 500);
                        } else {
                            $error.html('<div class="gptc-error-message">' + response.data.message + '</div>').fadeIn();
                        }
                    },
                    error: function(xhr, status, error) {
                        $error.html('<div class="gptc-error-message">' + gptc_ajax.error_text + '</div>').fadeIn();
                    },
                    complete: function() {
                        // Reset button state
                        $submitBtn.find('.gptc-btn-loading').hide();
                        $submitBtn.find('.gptc-btn-text').show();
                        $submitBtn.prop('disabled', false);
                    }
                });
            });
            
            // Auto-complete for county field
            $('#gptc_county').on('change', function() {
                var selectedCounty = $(this).val();
                if (selectedCounty) {
                    // You could add county-specific information here
                }
            });
        });
        </script>
        <?php
        
        return ob_get_clean();
    }
    
    /**
     * Get county options for select dropdown
     */
    private function get_county_options() {
        $counties = GPTC_County_Data::get_all_counties();
        $options = '';
        
        foreach ($counties as $county) {
            $options .= sprintf(
                '<option value="%s">%s County</option>',
                esc_attr($county['county_name']),
                esc_html($county['county_name'])
            );
        }
        
        return $options;
    }
    
    /**
     * Render calculation results
     */
    public static function render_results($calculation, $property_data = array()) {
        if (is_wp_error($calculation)) {
            return '<div class="gptc-error-message">' . esc_html($calculation->get_error_message()) . '</div>';
        }
        
        $formatted = GPTC_Calculator::format_calculation_results($calculation);
        
        ob_start();
        ?>
        <div class="gptc-results-container">
            <div class="gptc-results-header">
                <h4 class="gptc-results-title">
                    <?php _e('Property Tax Estimate', 'georgia-property-tax-calculator'); ?>
                </h4>
                <div class="gptc-results-county">
                    <?php printf(__('County: %s', 'georgia-property-tax-calculator'), esc_html($calculation['county_name'])); ?>
                </div>
            </div>
            
            <div class="gptc-results-main">
                <div class="gptc-result-highlight">
                    <div class="gptc-result-label"><?php _e('Estimated Annual Tax', 'georgia-property-tax-calculator'); ?></div>
                    <div class="gptc-result-value gptc-result-primary"><?php echo esc_html($formatted['annual_tax_formatted']); ?></div>
                </div>
                
                <div class="gptc-result-highlight">
                    <div class="gptc-result-label"><?php _e('Estimated Monthly Tax', 'georgia-property-tax-calculator'); ?></div>
                    <div class="gptc-result-value gptc-result-secondary"><?php echo esc_html($formatted['monthly_tax_formatted']); ?></div>
                </div>
            </div>
            
            <div class="gptc-results-breakdown">
                <h5 class="gptc-breakdown-title"><?php _e('Calculation Breakdown', 'georgia-property-tax-calculator'); ?></h5>
                
                <div class="gptc-breakdown-row">
                    <span class="gptc-breakdown-label"><?php _e('Property Value:', 'georgia-property-tax-calculator'); ?></span>
                    <span class="gptc-breakdown-value"><?php echo esc_html($formatted['property_value_formatted']); ?></span>
                </div>
                
                <div class="gptc-breakdown-row">
                    <span class="gptc-breakdown-label"><?php _e('Assessment Ratio:', 'georgia-property-tax-calculator'); ?></span>
                    <span class="gptc-breakdown-value"><?php echo esc_html($formatted['assessment_ratio_formatted']); ?></span>
                </div>
                
                <div class="gptc-breakdown-row">
                    <span class="gptc-breakdown-label"><?php _e('Assessed Value:', 'georgia-property-tax-calculator'); ?></span>
                    <span class="gptc-breakdown-value"><?php echo esc_html($formatted['assessed_value_formatted']); ?></span>
                </div>
                
                <div class="gptc-breakdown-row">
                    <span class="gptc-breakdown-label"><?php _e('Total Exemptions:', 'georgia-property-tax-calculator'); ?></span>
                    <span class="gptc-breakdown-value"><?php echo esc_html($formatted['total_exemptions_formatted']); ?></span>
                </div>
                
                <div class="gptc-breakdown-row">
                    <span class="gptc-breakdown-label"><?php _e('Taxable Value:', 'georgia-property-tax-calculator'); ?></span>
                    <span class="gptc-breakdown-value"><?php echo esc_html($formatted['taxable_value_formatted']); ?></span>
                </div>
                
                <div class="gptc-breakdown-row">
                    <span class="gptc-breakdown-label"><?php _e('Millage Rate:', 'georgia-property-tax-calculator'); ?></span>
                    <span class="gptc-breakdown-value"><?php echo esc_html($formatted['millage_rate_formatted']); ?></span>
                </div>
            </div>
            
            <?php if (!empty($property_data)): ?>
                <div class="gptc-property-info">
                    <h5 class="gptc-property-title"><?php _e('Property Information', 'georgia-property-tax-calculator'); ?></h5>
                    
                    <?php if (isset($property_data['formatted_address'])): ?>
                        <div class="gptc-property-row">
                            <span class="gptc-property-label"><?php _e('Address:', 'georgia-property-tax-calculator'); ?></span>
                            <span class="gptc-property-value"><?php echo esc_html($property_data['formatted_address']); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($property_data['property_value']) && isset($property_data['property_value']['confidence'])): ?>
                        <div class="gptc-property-row">
                            <span class="gptc-property-label"><?php _e('Value Confidence:', 'georgia-property-tax-calculator'); ?></span>
                            <span class="gptc-property-value"><?php echo esc_html(ucfirst($property_data['property_value']['confidence'])); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <div class="gptc-results-actions">
                <button type="button" class="gptc-btn gptc-btn-secondary" onclick="window.print();">
                    <?php _e('Print Results', 'georgia-property-tax-calculator'); ?>
                </button>
                
                <button type="button" class="gptc-btn gptc-btn-primary" onclick="gptcRequestConsultation();">
                    <?php _e('Request Consultation', 'georgia-property-tax-calculator'); ?>
                </button>
            </div>
            
            <div class="gptc-results-disclaimer">
                <p><strong><?php _e('Important:', 'georgia-property-tax-calculator'); ?></strong> 
                <?php _e('This is an estimate based on available data. Actual property taxes may differ due to local assessments, special districts, or other factors. Contact your county tax assessor for official information.', 'georgia-property-tax-calculator'); ?></p>
            </div>
        </div>
        
        <script>
        function gptcRequestConsultation() {
            var phone = '<?php echo esc_js(get_option('gptc_company_phone', '************')); ?>';
            var email = '<?php echo esc_js(get_option('gptc_business_email', '<EMAIL>')); ?>';
            
            if (confirm('<?php _e('Would you like to call us now for a consultation?', 'georgia-property-tax-calculator'); ?>')) {
                window.location.href = 'tel:' + phone;
            } else {
                window.location.href = 'mailto:' + email + '?subject=Property Tax Consultation Request';
            }
        }
        </script>
        <?php
        
        return ob_get_clean();
    }
    
    /**
     * Render tax scenarios
     */
    public static function render_scenarios($scenarios) {
        if (empty($scenarios)) {
            return '';
        }
        
        ob_start();
        ?>
        <div class="gptc-scenarios-container">
            <h4 class="gptc-scenarios-title"><?php _e('Tax Scenarios', 'georgia-property-tax-calculator'); ?></h4>
            <p class="gptc-scenarios-description">
                <?php _e('See how different exemptions affect your property tax:', 'georgia-property-tax-calculator'); ?>
            </p>
            
            <div class="gptc-scenarios-grid">
                <?php foreach ($scenarios as $scenario_key => $scenario): ?>
                    <div class="gptc-scenario-card">
                        <h5 class="gptc-scenario-title">
                            <?php
                            switch ($scenario_key) {
                                case 'no_exemptions':
                                    _e('No Exemptions', 'georgia-property-tax-calculator');
                                    break;
                                case 'homestead_only':
                                    _e('Homestead Only', 'georgia-property-tax-calculator');
                                    break;
                                case 'homestead_senior':
                                    _e('Homestead + Senior', 'georgia-property-tax-calculator');
                                    break;
                                case 'all_exemptions':
                                    _e('All Exemptions', 'georgia-property-tax-calculator');
                                    break;
                                default:
                                    echo esc_html(ucwords(str_replace('_', ' ', $scenario_key)));
                            }
                            ?>
                        </h5>
                        
                        <div class="gptc-scenario-tax">
                            <span class="gptc-scenario-amount">$<?php echo number_format($scenario['annual_tax'], 2); ?></span>
                            <span class="gptc-scenario-period"><?php _e('per year', 'georgia-property-tax-calculator'); ?></span>
                        </div>
                        
                        <div class="gptc-scenario-monthly">
                            $<?php echo number_format($scenario['monthly_tax'], 2); ?> <?php _e('per month', 'georgia-property-tax-calculator'); ?>
                        </div>
                        
                        <div class="gptc-scenario-exemptions">
                            <?php _e('Exemptions:', 'georgia-property-tax-calculator'); ?> $<?php echo number_format($scenario['exemptions']['total'], 0); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
        
        return ob_get_clean();
    }
}
