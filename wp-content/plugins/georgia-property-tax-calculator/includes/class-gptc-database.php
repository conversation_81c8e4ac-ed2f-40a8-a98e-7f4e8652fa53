<?php
/**
 * Database operations for Georgia Property Tax Calculator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GPTC_Database {
    
    /**
     * Create plugin database tables
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Table for form submissions
        $submissions_table = $wpdb->prefix . 'gptc_submissions';
        $submissions_sql = "CREATE TABLE $submissions_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            email varchar(255) NOT NULL,
            property_address text NOT NULL,
            county varchar(100) DEFAULT '',
            property_value decimal(12,2) DEFAULT 0.00,
            assessed_value decimal(12,2) DEFAULT 0.00,
            exemptions decimal(12,2) DEFAULT 0.00,
            millage_rate decimal(8,3) DEFAULT 0.000,
            estimated_tax decimal(12,2) DEFAULT 0.00,
            submission_date datetime DEFAULT CURRENT_TIMESTAMP,
            ip_address varchar(45) DEFAULT '',
            user_agent text DEFAULT '',
            status varchar(20) DEFAULT 'pending',
            PRIMARY KEY (id),
            KEY email (email),
            KEY county (county),
            KEY submission_date (submission_date)
        ) $charset_collate;";
        
        // Table for Georgia counties data
        $counties_table = $wpdb->prefix . 'gptc_counties';
        $counties_sql = "CREATE TABLE $counties_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            county_name varchar(100) NOT NULL,
            county_code varchar(10) DEFAULT '',
            millage_rate decimal(8,3) NOT NULL DEFAULT 32.500,
            homestead_exemption decimal(12,2) NOT NULL DEFAULT 2000.00,
            senior_exemption decimal(12,2) DEFAULT 0.00,
            disabled_exemption decimal(12,2) DEFAULT 0.00,
            assessment_ratio decimal(5,2) NOT NULL DEFAULT 40.00,
            last_updated datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY county_name (county_name),
            KEY county_code (county_code)
        ) $charset_collate;";
        
        // Table for API logs
        $api_logs_table = $wpdb->prefix . 'gptc_api_logs';
        $api_logs_sql = "CREATE TABLE $api_logs_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            api_type varchar(50) NOT NULL,
            request_data text DEFAULT '',
            response_data text DEFAULT '',
            status_code int(3) DEFAULT 0,
            error_message text DEFAULT '',
            execution_time decimal(8,4) DEFAULT 0.0000,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY api_type (api_type),
            KEY status_code (status_code),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        dbDelta($submissions_sql);
        dbDelta($counties_sql);
        dbDelta($api_logs_sql);
        
        // Insert default county data
        self::insert_default_counties();
    }
    
    /**
     * Drop plugin database tables
     */
    public static function drop_tables() {
        global $wpdb;
        
        $tables = array(
            $wpdb->prefix . 'gptc_submissions',
            $wpdb->prefix . 'gptc_counties',
            $wpdb->prefix . 'gptc_api_logs'
        );
        
        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
    }
    
    /**
     * Insert default Georgia counties data
     */
    private static function insert_default_counties() {
        global $wpdb;
        
        $counties_table = $wpdb->prefix . 'gptc_counties';
        
        // Check if data already exists
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $counties_table");
        if ($count > 0) {
            return;
        }
        
        // Georgia counties with sample millage rates
        $counties_data = array(
            array('Appling', 'APP', 28.5, 2000, 4000, 0, 40),
            array('Atkinson', 'ATK', 29.2, 2000, 4000, 0, 40),
            array('Bacon', 'BAC', 27.8, 2000, 4000, 0, 40),
            array('Baker', 'BAK', 26.9, 2000, 4000, 0, 40),
            array('Baldwin', 'BAL', 30.1, 2000, 4000, 0, 40),
            array('Banks', 'BAN', 25.8, 2000, 4000, 0, 40),
            array('Barrow', 'BAR', 28.9, 2000, 4000, 0, 40),
            array('Bartow', 'BRT', 27.6, 2000, 4000, 0, 40),
            array('Ben Hill', 'BEN', 29.4, 2000, 4000, 0, 40),
            array('Berrien', 'BER', 28.1, 2000, 4000, 0, 40),
            array('Bibb', 'BIB', 31.2, 2000, 4000, 0, 40),
            array('Bleckley', 'BLE', 27.9, 2000, 4000, 0, 40),
            array('Brantley', 'BRA', 26.7, 2000, 4000, 0, 40),
            array('Brooks', 'BRO', 28.3, 2000, 4000, 0, 40),
            array('Bryan', 'BRY', 29.8, 2000, 4000, 0, 40),
            array('Bulloch', 'BUL', 30.5, 2000, 4000, 0, 40),
            array('Burke', 'BUR', 27.4, 2000, 4000, 0, 40),
            array('Butts', 'BUT', 28.7, 2000, 4000, 0, 40),
            array('Calhoun', 'CAL', 26.8, 2000, 4000, 0, 40),
            array('Camden', 'CAM', 29.1, 2000, 4000, 0, 40),
            array('Candler', 'CAN', 27.5, 2000, 4000, 0, 40),
            array('Carroll', 'CAR', 28.4, 2000, 4000, 0, 40),
            array('Catoosa', 'CAT', 26.9, 2000, 4000, 0, 40),
            array('Charlton', 'CHA', 27.2, 2000, 4000, 0, 40),
            array('Chatham', 'CHT', 33.8, 2000, 4000, 0, 40),
            array('Chattahoochee', 'CHO', 25.6, 2000, 4000, 0, 40),
            array('Chattooga', 'CHG', 26.4, 2000, 4000, 0, 40),
            array('Cherokee', 'CHE', 29.7, 2000, 4000, 0, 40),
            array('Clarke', 'CLA', 31.5, 2000, 4000, 0, 40),
            array('Clay', 'CLY', 25.9, 2000, 4000, 0, 40),
            array('Clayton', 'CLT', 34.2, 2000, 4000, 0, 40),
            array('Clinch', 'CLI', 26.1, 2000, 4000, 0, 40),
            array('Cobb', 'COB', 30.8, 2000, 4000, 0, 40),
            array('Coffee', 'COF', 28.6, 2000, 4000, 0, 40),
            array('Colquitt', 'COL', 27.7, 2000, 4000, 0, 40),
            array('Columbia', 'COM', 29.3, 2000, 4000, 0, 40),
            array('Cook', 'COO', 26.8, 2000, 4000, 0, 40),
            array('Coweta', 'COW', 28.9, 2000, 4000, 0, 40),
            array('Crawford', 'CRA', 25.7, 2000, 4000, 0, 40),
            array('Crisp', 'CRI', 27.1, 2000, 4000, 0, 40),
            array('Dade', 'DAD', 24.8, 2000, 4000, 0, 40),
            array('Dawson', 'DAW', 26.3, 2000, 4000, 0, 40),
            array('Decatur', 'DEC', 28.2, 2000, 4000, 0, 40),
            array('DeKalb', 'DEK', 35.1, 2000, 4000, 0, 40),
            array('Dodge', 'DOD', 27.4, 2000, 4000, 0, 40),
            array('Dooly', 'DOO', 26.9, 2000, 4000, 0, 40),
            array('Dougherty', 'DOU', 32.4, 2000, 4000, 0, 40),
            array('Douglas', 'DGL', 30.6, 2000, 4000, 0, 40),
            array('Early', 'EAR', 25.8, 2000, 4000, 0, 40),
            array('Echols', 'ECH', 24.9, 2000, 4000, 0, 40),
            array('Effingham', 'EFF', 28.7, 2000, 4000, 0, 40),
            array('Elbert', 'ELB', 26.5, 2000, 4000, 0, 40),
            array('Emanuel', 'EMA', 27.3, 2000, 4000, 0, 40),
            array('Evans', 'EVA', 26.1, 2000, 4000, 0, 40),
            array('Fannin', 'FAN', 25.4, 2000, 4000, 0, 40),
            array('Fayette', 'FAY', 29.8, 2000, 4000, 0, 40),
            array('Floyd', 'FLO', 28.1, 2000, 4000, 0, 40),
            array('Forsyth', 'FOR', 27.9, 2000, 4000, 0, 40),
            array('Franklin', 'FRA', 26.7, 2000, 4000, 0, 40),
            array('Fulton', 'FUL', 32.5, 2000, 4000, 0, 40),
            array('Gilmer', 'GIL', 25.6, 2000, 4000, 0, 40),
            array('Glascock', 'GLA', 24.7, 2000, 4000, 0, 40),
            array('Glynn', 'GLY', 30.2, 2000, 4000, 0, 40),
            array('Gordon', 'GOR', 27.8, 2000, 4000, 0, 40),
            array('Grady', 'GRA', 26.4, 2000, 4000, 0, 40),
            array('Greene', 'GRE', 25.9, 2000, 4000, 0, 40),
            array('Gwinnett', 'GWI', 31.7, 2000, 4000, 0, 40),
            array('Habersham', 'HAB', 26.8, 2000, 4000, 0, 40),
            array('Hall', 'HAL', 28.5, 2000, 4000, 0, 40),
            array('Hancock', 'HAN', 25.2, 2000, 4000, 0, 40),
            array('Haralson', 'HAR', 26.1, 2000, 4000, 0, 40),
            array('Harris', 'HRS', 27.3, 2000, 4000, 0, 40),
            array('Hart', 'HRT', 25.7, 2000, 4000, 0, 40),
            array('Heard', 'HEA', 24.9, 2000, 4000, 0, 40),
            array('Henry', 'HEN', 30.4, 2000, 4000, 0, 40),
            array('Houston', 'HOU', 29.1, 2000, 4000, 0, 40),
            array('Irwin', 'IRW', 26.3, 2000, 4000, 0, 40),
            array('Jackson', 'JAC', 27.6, 2000, 4000, 0, 40),
            array('Jasper', 'JAS', 25.8, 2000, 4000, 0, 40),
            array('Jeff Davis', 'JEF', 26.9, 2000, 4000, 0, 40),
            array('Jefferson', 'JFF', 27.1, 2000, 4000, 0, 40),
            array('Jenkins', 'JEN', 25.4, 2000, 4000, 0, 40),
            array('Johnson', 'JOH', 26.2, 2000, 4000, 0, 40),
            array('Jones', 'JON', 27.8, 2000, 4000, 0, 40),
            array('Lamar', 'LAM', 26.5, 2000, 4000, 0, 40),
            array('Lanier', 'LAN', 25.1, 2000, 4000, 0, 40),
            array('Laurens', 'LAU', 28.3, 2000, 4000, 0, 40),
            array('Lee', 'LEE', 27.9, 2000, 4000, 0, 40),
            array('Liberty', 'LIB', 29.6, 2000, 4000, 0, 40),
            array('Lincoln', 'LIN', 24.8, 2000, 4000, 0, 40),
            array('Long', 'LON', 26.7, 2000, 4000, 0, 40),
            array('Lowndes', 'LOW', 30.8, 2000, 4000, 0, 40),
            array('Lumpkin', 'LUM', 25.9, 2000, 4000, 0, 40),
            array('Macon', 'MAC', 27.2, 2000, 4000, 0, 40),
            array('Madison', 'MAD', 26.4, 2000, 4000, 0, 40),
            array('Marion', 'MAR', 25.6, 2000, 4000, 0, 40),
            array('McDuffie', 'MCD', 26.8, 2000, 4000, 0, 40),
            array('McIntosh', 'MCI', 27.5, 2000, 4000, 0, 40),
            array('Meriwether', 'MER', 25.3, 2000, 4000, 0, 40),
            array('Miller', 'MIL', 26.1, 2000, 4000, 0, 40),
            array('Mitchell', 'MIT', 27.4, 2000, 4000, 0, 40),
            array('Monroe', 'MON', 28.7, 2000, 4000, 0, 40),
            array('Montgomery', 'MOG', 25.8, 2000, 4000, 0, 40),
            array('Morgan', 'MOR', 26.9, 2000, 4000, 0, 40),
            array('Murray', 'MUR', 25.2, 2000, 4000, 0, 40),
            array('Muscogee', 'MUS', 33.1, 2000, 4000, 0, 40),
            array('Newton', 'NEW', 29.3, 2000, 4000, 0, 40),
            array('Oconee', 'OCO', 27.1, 2000, 4000, 0, 40),
            array('Oglethorpe', 'OGL', 25.7, 2000, 4000, 0, 40),
            array('Paulding', 'PAU', 28.4, 2000, 4000, 0, 40),
            array('Peach', 'PEA', 27.6, 2000, 4000, 0, 40),
            array('Pickens', 'PIC', 26.3, 2000, 4000, 0, 40),
            array('Pierce', 'PIE', 27.8, 2000, 4000, 0, 40),
            array('Pike', 'PIK', 25.9, 2000, 4000, 0, 40),
            array('Polk', 'POL', 26.7, 2000, 4000, 0, 40),
            array('Pulaski', 'PUL', 25.4, 2000, 4000, 0, 40),
            array('Putnam', 'PUT', 27.2, 2000, 4000, 0, 40),
            array('Quitman', 'QUI', 24.6, 2000, 4000, 0, 40),
            array('Rabun', 'RAB', 25.8, 2000, 4000, 0, 40),
            array('Randolph', 'RAN', 24.9, 2000, 4000, 0, 40),
            array('Richmond', 'RIC', 32.7, 2000, 4000, 0, 40),
            array('Rockdale', 'ROC', 30.5, 2000, 4000, 0, 40),
            array('Schley', 'SCH', 25.1, 2000, 4000, 0, 40),
            array('Screven', 'SCR', 26.4, 2000, 4000, 0, 40),
            array('Seminole', 'SEM', 25.7, 2000, 4000, 0, 40),
            array('Spalding', 'SPA', 29.2, 2000, 4000, 0, 40),
            array('Stephens', 'STE', 26.8, 2000, 4000, 0, 40),
            array('Stewart', 'STW', 24.3, 2000, 4000, 0, 40),
            array('Sumter', 'SUM', 27.5, 2000, 4000, 0, 40),
            array('Talbot', 'TAL', 24.8, 2000, 4000, 0, 40),
            array('Taliaferro', 'TLF', 23.9, 2000, 4000, 0, 40),
            array('Tattnall', 'TAT', 26.6, 2000, 4000, 0, 40),
            array('Taylor', 'TAY', 25.2, 2000, 4000, 0, 40),
            array('Telfair', 'TEL', 25.9, 2000, 4000, 0, 40),
            array('Terrell', 'TER', 24.7, 2000, 4000, 0, 40),
            array('Thomas', 'THO', 28.1, 2000, 4000, 0, 40),
            array('Tift', 'TIF', 27.3, 2000, 4000, 0, 40),
            array('Toombs', 'TOO', 26.8, 2000, 4000, 0, 40),
            array('Towns', 'TOW', 24.9, 2000, 4000, 0, 40),
            array('Treutlen', 'TRE', 25.6, 2000, 4000, 0, 40),
            array('Troup', 'TRO', 28.7, 2000, 4000, 0, 40),
            array('Turner', 'TUR', 26.2, 2000, 4000, 0, 40),
            array('Twiggs', 'TWI', 25.4, 2000, 4000, 0, 40),
            array('Union', 'UNI', 24.1, 2000, 4000, 0, 40),
            array('Upson', 'UPS', 26.9, 2000, 4000, 0, 40),
            array('Walker', 'WAL', 25.7, 2000, 4000, 0, 40),
            array('Walton', 'WLT', 27.4, 2000, 4000, 0, 40),
            array('Ware', 'WAR', 28.6, 2000, 4000, 0, 40),
            array('Warren', 'WRN', 24.5, 2000, 4000, 0, 40),
            array('Washington', 'WAS', 26.1, 2000, 4000, 0, 40),
            array('Wayne', 'WAY', 27.8, 2000, 4000, 0, 40),
            array('Webster', 'WEB', 23.7, 2000, 4000, 0, 40),
            array('Wheeler', 'WHE', 25.3, 2000, 4000, 0, 40),
            array('White', 'WHI', 25.8, 2000, 4000, 0, 40),
            array('Whitfield', 'WHF', 27.2, 2000, 4000, 0, 40),
            array('Wilcox', 'WIL', 25.9, 2000, 4000, 0, 40),
            array('Wilkes', 'WLK', 24.6, 2000, 4000, 0, 40),
            array('Wilkinson', 'WLN', 25.1, 2000, 4000, 0, 40),
            array('Worth', 'WOR', 26.7, 2000, 4000, 0, 40)
        );
        
        foreach ($counties_data as $county) {
            $wpdb->insert(
                $counties_table,
                array(
                    'county_name' => $county[0],
                    'county_code' => $county[1],
                    'millage_rate' => $county[2],
                    'homestead_exemption' => $county[3],
                    'senior_exemption' => $county[4],
                    'disabled_exemption' => $county[5],
                    'assessment_ratio' => $county[6]
                ),
                array('%s', '%s', '%f', '%f', '%f', '%f', '%f')
            );
        }
    }
    
    /**
     * Save form submission
     */
    public static function save_submission($data) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'gptc_submissions';
        
        $result = $wpdb->insert(
            $table,
            array(
                'name' => sanitize_text_field($data['name']),
                'email' => sanitize_email($data['email']),
                'property_address' => sanitize_textarea_field($data['property_address']),
                'county' => sanitize_text_field($data['county']),
                'property_value' => floatval($data['property_value']),
                'assessed_value' => floatval($data['assessed_value']),
                'exemptions' => floatval($data['exemptions']),
                'millage_rate' => floatval($data['millage_rate']),
                'estimated_tax' => floatval($data['estimated_tax']),
                'ip_address' => self::get_client_ip(),
                'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? ''),
                'status' => 'completed'
            ),
            array('%s', '%s', '%s', '%s', '%f', '%f', '%f', '%f', '%f', '%s', '%s', '%s')
        );
        
        return $result !== false ? $wpdb->insert_id : false;
    }
    
    /**
     * Log API request
     */
    public static function log_api_request($api_type, $request_data, $response_data, $status_code, $error_message = '', $execution_time = 0) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'gptc_api_logs';
        
        $wpdb->insert(
            $table,
            array(
                'api_type' => sanitize_text_field($api_type),
                'request_data' => wp_json_encode($request_data),
                'response_data' => wp_json_encode($response_data),
                'status_code' => intval($status_code),
                'error_message' => sanitize_textarea_field($error_message),
                'execution_time' => floatval($execution_time)
            ),
            array('%s', '%s', '%s', '%d', '%s', '%f')
        );
    }
    
    /**
     * Get county data by name
     */
    public static function get_county_data($county_name) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'gptc_counties';
        
        return $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table WHERE county_name = %s",
                $county_name
            ),
            ARRAY_A
        );
    }
    
    /**
     * Get client IP address
     */
    private static function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}
