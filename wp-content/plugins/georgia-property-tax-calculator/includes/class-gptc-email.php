<?php
/**
 * Email Notification System for Georgia Property Tax Calculator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GPTC_Email {
    
    /**
     * Send calculation results to user and business
     */
    public static function send_calculation_emails($user_data, $calculation, $property_data = array()) {
        $user_email_sent = self::send_user_email($user_data, $calculation, $property_data);
        $business_email_sent = self::send_business_email($user_data, $calculation, $property_data);
        
        return array(
            'user_email' => $user_email_sent,
            'business_email' => $business_email_sent
        );
    }
    
    /**
     * Send email to user with calculation results
     */
    public static function send_user_email($user_data, $calculation, $property_data = array()) {
        $to = sanitize_email($user_data['email']);
        $subject = __('Your Georgia Property Tax Estimate', 'georgia-property-tax-calculator');
        
        $message = self::get_user_email_template($user_data, $calculation, $property_data);
        $headers = self::get_email_headers();
        
        return wp_mail($to, $subject, $message, $headers);
    }
    
    /**
     * Send notification email to business
     */
    public static function send_business_email($user_data, $calculation, $property_data = array()) {
        $business_email = get_option('gptc_business_email', '<EMAIL>');
        $to = sanitize_email($business_email);
        $subject = sprintf(__('New Property Tax Calculation - %s', 'georgia-property-tax-calculator'), $user_data['name']);
        
        $message = self::get_business_email_template($user_data, $calculation, $property_data);
        $headers = self::get_email_headers();
        
        return wp_mail($to, $subject, $message, $headers);
    }
    
    /**
     * Get user email template
     */
    private static function get_user_email_template($user_data, $calculation, $property_data = array()) {
        $company_name = get_option('gptc_company_name', 'Property Tax Reports USA');
        $company_phone = get_option('gptc_company_phone', '************');
        $company_logo = get_option('gptc_company_logo', '');
        $business_email = get_option('gptc_business_email', '<EMAIL>');
        
        $formatted = GPTC_Calculator::format_calculation_results($calculation);
        
        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title><?php _e('Your Property Tax Estimate', 'georgia-property-tax-calculator'); ?></title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #2c5aa0; color: white; padding: 20px; text-align: center; }
                .logo { max-width: 200px; height: auto; margin-bottom: 10px; }
                .content { padding: 20px; background: #f9f9f9; }
                .results-box { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #2c5aa0; }
                .highlight { font-size: 24px; font-weight: bold; color: #2c5aa0; margin: 10px 0; }
                .breakdown { margin: 20px 0; }
                .breakdown-row { display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #eee; }
                .footer { background: #333; color: white; padding: 20px; text-align: center; }
                .cta-button { display: inline-block; background: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
                .disclaimer { font-size: 12px; color: #666; margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 3px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <?php if (!empty($company_logo)): ?>
                        <img src="<?php echo esc_url($company_logo); ?>" alt="<?php echo esc_attr($company_name); ?>" class="logo">
                    <?php endif; ?>
                    <h1><?php echo esc_html($company_name); ?></h1>
                    <p><?php _e('Your Georgia Property Tax Estimate', 'georgia-property-tax-calculator'); ?></p>
                </div>
                
                <div class="content">
                    <h2><?php printf(__('Hello %s,', 'georgia-property-tax-calculator'), esc_html($user_data['name'])); ?></h2>
                    
                    <p><?php _e('Thank you for using our Georgia Property Tax Calculator. Below is your personalized property tax estimate:', 'georgia-property-tax-calculator'); ?></p>
                    
                    <div class="results-box">
                        <h3><?php _e('Property Tax Estimate', 'georgia-property-tax-calculator'); ?></h3>
                        
                        <?php if (isset($property_data['formatted_address'])): ?>
                            <p><strong><?php _e('Property Address:', 'georgia-property-tax-calculator'); ?></strong><br>
                            <?php echo esc_html($property_data['formatted_address']); ?></p>
                        <?php else: ?>
                            <p><strong><?php _e('Property Address:', 'georgia-property-tax-calculator'); ?></strong><br>
                            <?php echo esc_html($user_data['address']); ?></p>
                        <?php endif; ?>
                        
                        <p><strong><?php _e('County:', 'georgia-property-tax-calculator'); ?></strong> <?php echo esc_html($calculation['county_name']); ?></p>
                        
                        <div class="highlight">
                            <?php _e('Estimated Annual Tax:', 'georgia-property-tax-calculator'); ?> <?php echo esc_html($formatted['annual_tax_formatted']); ?>
                        </div>
                        
                        <div class="highlight" style="font-size: 18px;">
                            <?php _e('Estimated Monthly Tax:', 'georgia-property-tax-calculator'); ?> <?php echo esc_html($formatted['monthly_tax_formatted']); ?>
                        </div>
                        
                        <div class="breakdown">
                            <h4><?php _e('Calculation Breakdown:', 'georgia-property-tax-calculator'); ?></h4>
                            
                            <div class="breakdown-row">
                                <span><?php _e('Property Value:', 'georgia-property-tax-calculator'); ?></span>
                                <span><?php echo esc_html($formatted['property_value_formatted']); ?></span>
                            </div>
                            
                            <div class="breakdown-row">
                                <span><?php _e('Assessment Ratio:', 'georgia-property-tax-calculator'); ?></span>
                                <span><?php echo esc_html($formatted['assessment_ratio_formatted']); ?></span>
                            </div>
                            
                            <div class="breakdown-row">
                                <span><?php _e('Assessed Value:', 'georgia-property-tax-calculator'); ?></span>
                                <span><?php echo esc_html($formatted['assessed_value_formatted']); ?></span>
                            </div>
                            
                            <div class="breakdown-row">
                                <span><?php _e('Total Exemptions:', 'georgia-property-tax-calculator'); ?></span>
                                <span><?php echo esc_html($formatted['total_exemptions_formatted']); ?></span>
                            </div>
                            
                            <div class="breakdown-row">
                                <span><?php _e('Taxable Value:', 'georgia-property-tax-calculator'); ?></span>
                                <span><?php echo esc_html($formatted['taxable_value_formatted']); ?></span>
                            </div>
                            
                            <div class="breakdown-row">
                                <span><?php _e('Millage Rate:', 'georgia-property-tax-calculator'); ?></span>
                                <span><?php echo esc_html($formatted['millage_rate_formatted']); ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <h3><?php _e('Need Help with Your Property Taxes?', 'georgia-property-tax-calculator'); ?></h3>
                        <p><?php _e('Our experts can help you understand your property tax assessment, find additional exemptions, and potentially reduce your tax burden.', 'georgia-property-tax-calculator'); ?></p>
                        
                        <a href="tel:<?php echo esc_attr($company_phone); ?>" class="cta-button">
                            <?php printf(__('Call Us: %s', 'georgia-property-tax-calculator'), esc_html($company_phone)); ?>
                        </a>
                        
                        <br>
                        
                        <a href="mailto:<?php echo esc_attr($business_email); ?>?subject=Property Tax Consultation Request" class="cta-button">
                            <?php _e('Request Consultation', 'georgia-property-tax-calculator'); ?>
                        </a>
                    </div>
                    
                    <div class="disclaimer">
                        <strong><?php _e('Important Disclaimer:', 'georgia-property-tax-calculator'); ?></strong><br>
                        <?php _e('This estimate is based on available data and standard calculation methods. Actual property taxes may vary due to local assessments, special tax districts, appeals, or other factors. For official tax information, please contact your county tax assessor. This estimate should not be used for legal or financial decisions without professional consultation.', 'georgia-property-tax-calculator'); ?>
                    </div>
                </div>
                
                <div class="footer">
                    <p><strong><?php echo esc_html($company_name); ?></strong></p>
                    <p><?php _e('Phone:', 'georgia-property-tax-calculator'); ?> <?php echo esc_html($company_phone); ?></p>
                    <p><?php _e('Email:', 'georgia-property-tax-calculator'); ?> <?php echo esc_html($business_email); ?></p>
                    <p style="font-size: 12px; margin-top: 20px;">
                        <?php _e('You received this email because you requested a property tax estimate from our website. If you have any questions, please contact us using the information above.', 'georgia-property-tax-calculator'); ?>
                    </p>
                </div>
            </div>
        </body>
        </html>
        <?php
        
        return ob_get_clean();
    }
    
    /**
     * Get business email template
     */
    private static function get_business_email_template($user_data, $calculation, $property_data = array()) {
        $formatted = GPTC_Calculator::format_calculation_results($calculation);
        
        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title><?php _e('New Property Tax Calculation', 'georgia-property-tax-calculator'); ?></title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #2c5aa0; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .info-box { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #2c5aa0; }
                .breakdown-row { display: flex; justify-content: space-between; padding: 3px 0; border-bottom: 1px solid #eee; }
                .highlight { font-weight: bold; color: #2c5aa0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1><?php _e('New Property Tax Calculation', 'georgia-property-tax-calculator'); ?></h1>
                    <p><?php echo esc_html(current_time('F j, Y g:i A')); ?></p>
                </div>
                
                <div class="content">
                    <div class="info-box">
                        <h3><?php _e('Customer Information', 'georgia-property-tax-calculator'); ?></h3>
                        <p><strong><?php _e('Name:', 'georgia-property-tax-calculator'); ?></strong> <?php echo esc_html($user_data['name']); ?></p>
                        <p><strong><?php _e('Email:', 'georgia-property-tax-calculator'); ?></strong> <a href="mailto:<?php echo esc_attr($user_data['email']); ?>"><?php echo esc_html($user_data['email']); ?></a></p>
                        <p><strong><?php _e('Property Address:', 'georgia-property-tax-calculator'); ?></strong><br>
                        <?php echo esc_html($user_data['address']); ?></p>
                        
                        <?php if (isset($property_data['formatted_address'])): ?>
                            <p><strong><?php _e('Geocoded Address:', 'georgia-property-tax-calculator'); ?></strong><br>
                            <?php echo esc_html($property_data['formatted_address']); ?></p>
                        <?php endif; ?>
                    </div>
                    
                    <div class="info-box">
                        <h3><?php _e('Calculation Results', 'georgia-property-tax-calculator'); ?></h3>
                        <p><strong><?php _e('County:', 'georgia-property-tax-calculator'); ?></strong> <?php echo esc_html($calculation['county_name']); ?></p>
                        
                        <div class="breakdown-row">
                            <span><?php _e('Property Value:', 'georgia-property-tax-calculator'); ?></span>
                            <span class="highlight"><?php echo esc_html($formatted['property_value_formatted']); ?></span>
                        </div>
                        
                        <div class="breakdown-row">
                            <span><?php _e('Assessed Value:', 'georgia-property-tax-calculator'); ?></span>
                            <span><?php echo esc_html($formatted['assessed_value_formatted']); ?></span>
                        </div>
                        
                        <div class="breakdown-row">
                            <span><?php _e('Total Exemptions:', 'georgia-property-tax-calculator'); ?></span>
                            <span><?php echo esc_html($formatted['total_exemptions_formatted']); ?></span>
                        </div>
                        
                        <div class="breakdown-row">
                            <span><?php _e('Taxable Value:', 'georgia-property-tax-calculator'); ?></span>
                            <span><?php echo esc_html($formatted['taxable_value_formatted']); ?></span>
                        </div>
                        
                        <div class="breakdown-row">
                            <span><?php _e('Millage Rate:', 'georgia-property-tax-calculator'); ?></span>
                            <span><?php echo esc_html($formatted['millage_rate_formatted']); ?></span>
                        </div>
                        
                        <div class="breakdown-row" style="border-top: 2px solid #2c5aa0; margin-top: 10px; padding-top: 10px;">
                            <span><strong><?php _e('Annual Tax:', 'georgia-property-tax-calculator'); ?></strong></span>
                            <span class="highlight" style="font-size: 18px;"><?php echo esc_html($formatted['annual_tax_formatted']); ?></span>
                        </div>
                        
                        <div class="breakdown-row">
                            <span><strong><?php _e('Monthly Tax:', 'georgia-property-tax-calculator'); ?></strong></span>
                            <span class="highlight"><?php echo esc_html($formatted['monthly_tax_formatted']); ?></span>
                        </div>
                    </div>
                    
                    <?php if (!empty($property_data)): ?>
                        <div class="info-box">
                            <h3><?php _e('API Data', 'georgia-property-tax-calculator'); ?></h3>
                            
                            <?php if (isset($property_data['property_value'])): ?>
                                <p><strong><?php _e('Property Value Source:', 'georgia-property-tax-calculator'); ?></strong> 
                                <?php echo isset($property_data['property_value']['is_fallback']) ? __('Estimated (API unavailable)', 'georgia-property-tax-calculator') : __('RentCast API', 'georgia-property-tax-calculator'); ?></p>
                                
                                <?php if (isset($property_data['property_value']['confidence'])): ?>
                                    <p><strong><?php _e('Value Confidence:', 'georgia-property-tax-calculator'); ?></strong> <?php echo esc_html(ucfirst($property_data['property_value']['confidence'])); ?></p>
                                <?php endif; ?>
                            <?php endif; ?>
                            
                            <?php if (isset($property_data['geocoding'])): ?>
                                <p><strong><?php _e('Geocoding:', 'georgia-property-tax-calculator'); ?></strong> <?php _e('Successful', 'georgia-property-tax-calculator'); ?></p>
                                <p><strong><?php _e('Coordinates:', 'georgia-property-tax-calculator'); ?></strong> 
                                <?php echo esc_html($property_data['geocoding']['latitude']); ?>, <?php echo esc_html($property_data['geocoding']['longitude']); ?></p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="info-box">
                        <h3><?php _e('Follow-up Actions', 'georgia-property-tax-calculator'); ?></h3>
                        <ul>
                            <li><?php _e('Customer has been sent detailed results via email', 'georgia-property-tax-calculator'); ?></li>
                            <li><?php _e('Consider following up within 24-48 hours', 'georgia-property-tax-calculator'); ?></li>
                            <li><?php _e('Potential services: Tax appeal, exemption review, consultation', 'georgia-property-tax-calculator'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </body>
        </html>
        <?php
        
        return ob_get_clean();
    }
    
    /**
     * Get email headers
     */
    private static function get_email_headers() {
        $company_name = get_option('gptc_company_name', 'Property Tax Reports USA');
        $business_email = get_option('gptc_business_email', '<EMAIL>');
        
        $headers = array();
        $headers[] = 'Content-Type: text/html; charset=UTF-8';
        $headers[] = sprintf('From: %s <%s>', $company_name, $business_email);
        $headers[] = sprintf('Reply-To: %s', $business_email);
        
        return $headers;
    }
    
    /**
     * Send test email
     */
    public static function send_test_email($to_email) {
        $subject = __('Test Email from Georgia Property Tax Calculator', 'georgia-property-tax-calculator');
        $message = self::get_test_email_template();
        $headers = self::get_email_headers();
        
        return wp_mail($to_email, $subject, $message, $headers);
    }
    
    /**
     * Get test email template
     */
    private static function get_test_email_template() {
        $company_name = get_option('gptc_company_name', 'Property Tax Reports USA');
        $company_phone = get_option('gptc_company_phone', '************');
        $business_email = get_option('gptc_business_email', '<EMAIL>');
        
        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title><?php _e('Test Email', 'georgia-property-tax-calculator'); ?></title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; background: #f9f9f9; border-radius: 5px; }
                .header { background: #2c5aa0; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
                .content { padding: 20px; background: white; border-radius: 0 0 5px 5px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1><?php echo esc_html($company_name); ?></h1>
                    <p><?php _e('Email System Test', 'georgia-property-tax-calculator'); ?></p>
                </div>
                <div class="content">
                    <h2><?php _e('Email Configuration Test', 'georgia-property-tax-calculator'); ?></h2>
                    <p><?php _e('This is a test email to verify that your email configuration is working correctly.', 'georgia-property-tax-calculator'); ?></p>
                    
                    <p><strong><?php _e('Company Information:', 'georgia-property-tax-calculator'); ?></strong></p>
                    <ul>
                        <li><?php _e('Company:', 'georgia-property-tax-calculator'); ?> <?php echo esc_html($company_name); ?></li>
                        <li><?php _e('Phone:', 'georgia-property-tax-calculator'); ?> <?php echo esc_html($company_phone); ?></li>
                        <li><?php _e('Email:', 'georgia-property-tax-calculator'); ?> <?php echo esc_html($business_email); ?></li>
                    </ul>
                    
                    <p><?php _e('If you received this email, your email system is configured correctly!', 'georgia-property-tax-calculator'); ?></p>
                    
                    <p><em><?php printf(__('Test sent on: %s', 'georgia-property-tax-calculator'), current_time('F j, Y g:i A')); ?></em></p>
                </div>
            </div>
        </body>
        </html>
        <?php
        
        return ob_get_clean();
    }
}
