<?php
/**
 * Admin functionality for Georgia Property Tax Calculator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GPTC_Admin {
    
    /**
     * Single instance
     */
    private static $instance = null;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_notices', array($this, 'admin_notices'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            __('Property Tax Estimator Settings', 'georgia-property-tax-calculator'),
            __('Property Tax Estimator', 'georgia-property-tax-calculator'),
            'manage_options',
            'gptc-settings',
            array($this, 'settings_page')
        );
        
        add_management_page(
            __('Tax Calculator Submissions', 'georgia-property-tax-calculator'),
            __('Tax Calculator Submissions', 'georgia-property-tax-calculator'),
            'manage_options',
            'gptc-submissions',
            array($this, 'submissions_page')
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        // API Settings Section
        add_settings_section(
            'gptc_api_settings',
            __('API Configuration', 'georgia-property-tax-calculator'),
            array($this, 'api_settings_callback'),
            'gptc-settings'
        );
        
        // Google Maps API Key
        add_settings_field(
            'gptc_google_maps_api_key',
            __('Google Maps API Key', 'georgia-property-tax-calculator'),
            array($this, 'google_maps_api_key_callback'),
            'gptc-settings',
            'gptc_api_settings'
        );
        
        // RentCast API Key
        add_settings_field(
            'gptc_rentcast_api_key',
            __('RentCast API Key', 'georgia-property-tax-calculator'),
            array($this, 'rentcast_api_key_callback'),
            'gptc-settings',
            'gptc_api_settings'
        );
        
        // Business Settings Section
        add_settings_section(
            'gptc_business_settings',
            __('Business Information', 'georgia-property-tax-calculator'),
            array($this, 'business_settings_callback'),
            'gptc-settings'
        );
        
        // Business Email
        add_settings_field(
            'gptc_business_email',
            __('Business Email', 'georgia-property-tax-calculator'),
            array($this, 'business_email_callback'),
            'gptc-settings',
            'gptc_business_settings'
        );
        
        // Company Phone
        add_settings_field(
            'gptc_company_phone',
            __('Company Phone', 'georgia-property-tax-calculator'),
            array($this, 'company_phone_callback'),
            'gptc-settings',
            'gptc_business_settings'
        );
        
        // Company Logo
        add_settings_field(
            'gptc_company_logo',
            __('Company Logo URL', 'georgia-property-tax-calculator'),
            array($this, 'company_logo_callback'),
            'gptc-settings',
            'gptc_business_settings'
        );
        
        // Register settings
        register_setting('gptc_settings_group', 'gptc_google_maps_api_key', array($this, 'sanitize_api_key'));
        register_setting('gptc_settings_group', 'gptc_rentcast_api_key', array($this, 'sanitize_api_key'));
        register_setting('gptc_settings_group', 'gptc_business_email', 'sanitize_email');
        register_setting('gptc_settings_group', 'gptc_company_phone', 'sanitize_text_field');
        register_setting('gptc_settings_group', 'gptc_company_logo', 'esc_url_raw');
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }
        
        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['gptc_settings_nonce'], 'gptc_save_settings')) {
            $this->save_settings();
        }
        
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <div class="gptc-admin-header">
                <h2><?php _e('Georgia Property Tax Calculator Settings', 'georgia-property-tax-calculator'); ?></h2>
                <p><?php _e('Configure your API keys and business information for the property tax calculator.', 'georgia-property-tax-calculator'); ?></p>
            </div>
            
            <form method="post" action="">
                <?php wp_nonce_field('gptc_save_settings', 'gptc_settings_nonce'); ?>
                
                <table class="form-table">
                    <tbody>
                        <tr>
                            <th scope="row">
                                <label for="gptc_google_maps_api_key"><?php _e('Google Maps API Key', 'georgia-property-tax-calculator'); ?></label>
                            </th>
                            <td>
                                <input type="password" id="gptc_google_maps_api_key" name="gptc_google_maps_api_key" 
                                       value="<?php echo esc_attr($this->get_masked_api_key('gptc_google_maps_api_key')); ?>" 
                                       class="regular-text" />
                                <p class="description">
                                    <?php _e('Enter your Google Maps Geocoding API key. Get one at:', 'georgia-property-tax-calculator'); ?>
                                    <a href="https://console.cloud.google.com" target="_blank">Google Cloud Console</a>
                                </p>
                                <button type="button" class="button" onclick="toggleApiKey('gptc_google_maps_api_key')">
                                    <?php _e('Show/Hide Key', 'georgia-property-tax-calculator'); ?>
                                </button>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="gptc_rentcast_api_key"><?php _e('RentCast API Key', 'georgia-property-tax-calculator'); ?></label>
                            </th>
                            <td>
                                <input type="password" id="gptc_rentcast_api_key" name="gptc_rentcast_api_key" 
                                       value="<?php echo esc_attr($this->get_masked_api_key('gptc_rentcast_api_key')); ?>" 
                                       class="regular-text" />
                                <p class="description">
                                    <?php _e('Enter your RentCast API key. Get one at:', 'georgia-property-tax-calculator'); ?>
                                    <a href="https://www.rentcast.io" target="_blank">RentCast.io</a>
                                </p>
                                <button type="button" class="button" onclick="toggleApiKey('gptc_rentcast_api_key')">
                                    <?php _e('Show/Hide Key', 'georgia-property-tax-calculator'); ?>
                                </button>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="gptc_business_email"><?php _e('Business Email', 'georgia-property-tax-calculator'); ?></label>
                            </th>
                            <td>
                                <input type="email" id="gptc_business_email" name="gptc_business_email" 
                                       value="<?php echo esc_attr(get_option('gptc_business_email', '<EMAIL>')); ?>" 
                                       class="regular-text" />
                                <p class="description">
                                    <?php _e('Email address where form submissions will be sent.', 'georgia-property-tax-calculator'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="gptc_company_phone"><?php _e('Company Phone', 'georgia-property-tax-calculator'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="gptc_company_phone" name="gptc_company_phone" 
                                       value="<?php echo esc_attr(get_option('gptc_company_phone', '************')); ?>" 
                                       class="regular-text" />
                                <p class="description">
                                    <?php _e('Phone number to display in email notifications.', 'georgia-property-tax-calculator'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="gptc_company_logo"><?php _e('Company Logo URL', 'georgia-property-tax-calculator'); ?></label>
                            </th>
                            <td>
                                <input type="url" id="gptc_company_logo" name="gptc_company_logo" 
                                       value="<?php echo esc_attr(get_option('gptc_company_logo', '')); ?>" 
                                       class="regular-text" />
                                <p class="description">
                                    <?php _e('URL to your company logo for email notifications.', 'georgia-property-tax-calculator'); ?>
                                </p>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="gptc-test-section">
                    <h3><?php _e('Test API Connections', 'georgia-property-tax-calculator'); ?></h3>
                    <p><?php _e('Test your API keys to ensure they are working correctly.', 'georgia-property-tax-calculator'); ?></p>
                    
                    <button type="button" id="test-google-maps" class="button">
                        <?php _e('Test Google Maps API', 'georgia-property-tax-calculator'); ?>
                    </button>
                    
                    <button type="button" id="test-rentcast" class="button">
                        <?php _e('Test RentCast API', 'georgia-property-tax-calculator'); ?>
                    </button>
                    
                    <div id="api-test-results"></div>
                </div>
                
                <?php submit_button(__('Save Settings', 'georgia-property-tax-calculator')); ?>
            </form>
            
            <div class="gptc-usage-stats">
                <h3><?php _e('Usage Statistics', 'georgia-property-tax-calculator'); ?></h3>
                <?php $this->display_usage_stats(); ?>
            </div>
        </div>
        
        <script>
        function toggleApiKey(fieldId) {
            var field = document.getElementById(fieldId);
            if (field.type === 'password') {
                field.type = 'text';
            } else {
                field.type = 'password';
            }
        }
        
        // Test API connections
        jQuery(document).ready(function($) {
            $('#test-google-maps').click(function() {
                testApiConnection('google_maps');
            });
            
            $('#test-rentcast').click(function() {
                testApiConnection('rentcast');
            });
            
            function testApiConnection(apiType) {
                var button = $('#test-' + apiType.replace('_', '-'));
                var originalText = button.text();
                
                button.text('Testing...').prop('disabled', true);
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'gptc_test_api',
                        api_type: apiType,
                        nonce: '<?php echo wp_create_nonce('gptc_test_api'); ?>'
                    },
                    success: function(response) {
                        var resultDiv = $('#api-test-results');
                        if (response.success) {
                            resultDiv.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                        } else {
                            resultDiv.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                        }
                    },
                    error: function() {
                        $('#api-test-results').html('<div class="notice notice-error"><p>Test failed. Please try again.</p></div>');
                    },
                    complete: function() {
                        button.text(originalText).prop('disabled', false);
                    }
                });
            }
        });
        </script>
        <?php
    }
    
    /**
     * Submissions page
     */
    public function submissions_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }
        
        global $wpdb;
        $table = $wpdb->prefix . 'gptc_submissions';
        
        // Get submissions with pagination
        $per_page = 20;
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $offset = ($current_page - 1) * $per_page;
        
        $total_items = $wpdb->get_var("SELECT COUNT(*) FROM $table");
        $submissions = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table ORDER BY submission_date DESC LIMIT %d OFFSET %d",
                $per_page,
                $offset
            )
        );
        
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <div class="tablenav top">
                <div class="alignleft actions">
                    <span class="displaying-num"><?php printf(__('%d items', 'georgia-property-tax-calculator'), $total_items); ?></span>
                </div>
                <?php
                if ($total_items > $per_page) {
                    $total_pages = ceil($total_items / $per_page);
                    echo paginate_links(array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'prev_text' => __('&laquo;'),
                        'next_text' => __('&raquo;'),
                        'total' => $total_pages,
                        'current' => $current_page
                    ));
                }
                ?>
            </div>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Date', 'georgia-property-tax-calculator'); ?></th>
                        <th><?php _e('Name', 'georgia-property-tax-calculator'); ?></th>
                        <th><?php _e('Email', 'georgia-property-tax-calculator'); ?></th>
                        <th><?php _e('Address', 'georgia-property-tax-calculator'); ?></th>
                        <th><?php _e('County', 'georgia-property-tax-calculator'); ?></th>
                        <th><?php _e('Property Value', 'georgia-property-tax-calculator'); ?></th>
                        <th><?php _e('Estimated Tax', 'georgia-property-tax-calculator'); ?></th>
                        <th><?php _e('Status', 'georgia-property-tax-calculator'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($submissions)): ?>
                        <tr>
                            <td colspan="8"><?php _e('No submissions found.', 'georgia-property-tax-calculator'); ?></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($submissions as $submission): ?>
                            <tr>
                                <td><?php echo esc_html(date('M j, Y g:i A', strtotime($submission->submission_date))); ?></td>
                                <td><?php echo esc_html($submission->name); ?></td>
                                <td><a href="mailto:<?php echo esc_attr($submission->email); ?>"><?php echo esc_html($submission->email); ?></a></td>
                                <td><?php echo esc_html($submission->property_address); ?></td>
                                <td><?php echo esc_html($submission->county); ?></td>
                                <td>$<?php echo number_format($submission->property_value, 0); ?></td>
                                <td>$<?php echo number_format($submission->estimated_tax, 2); ?></td>
                                <td>
                                    <span class="status-<?php echo esc_attr($submission->status); ?>">
                                        <?php echo esc_html(ucfirst($submission->status)); ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <?php
    }
    
    /**
     * Save settings
     */
    private function save_settings() {
        if (isset($_POST['gptc_google_maps_api_key'])) {
            update_option('gptc_google_maps_api_key', sanitize_text_field($_POST['gptc_google_maps_api_key']));
        }
        
        if (isset($_POST['gptc_rentcast_api_key'])) {
            update_option('gptc_rentcast_api_key', sanitize_text_field($_POST['gptc_rentcast_api_key']));
        }
        
        if (isset($_POST['gptc_business_email'])) {
            update_option('gptc_business_email', sanitize_email($_POST['gptc_business_email']));
        }
        
        if (isset($_POST['gptc_company_phone'])) {
            update_option('gptc_company_phone', sanitize_text_field($_POST['gptc_company_phone']));
        }
        
        if (isset($_POST['gptc_company_logo'])) {
            update_option('gptc_company_logo', esc_url_raw($_POST['gptc_company_logo']));
        }
        
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully!', 'georgia-property-tax-calculator') . '</p></div>';
        });
    }
    
    /**
     * Get masked API key for display
     */
    private function get_masked_api_key($option_name) {
        $key = get_option($option_name, '');
        if (empty($key)) {
            return '';
        }
        
        $length = strlen($key);
        if ($length <= 8) {
            return str_repeat('*', $length);
        }
        
        return substr($key, 0, 4) . str_repeat('*', $length - 8) . substr($key, -4);
    }
    
    /**
     * Display usage statistics
     */
    private function display_usage_stats() {
        global $wpdb;
        
        $submissions_table = $wpdb->prefix . 'gptc_submissions';
        $api_logs_table = $wpdb->prefix . 'gptc_api_logs';
        
        // Get statistics
        $total_submissions = $wpdb->get_var("SELECT COUNT(*) FROM $submissions_table");
        $submissions_today = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $submissions_table WHERE DATE(submission_date) = %s",
                current_time('Y-m-d')
            )
        );
        $submissions_this_month = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $submissions_table WHERE YEAR(submission_date) = %d AND MONTH(submission_date) = %d",
                current_time('Y'),
                current_time('n')
            )
        );
        
        $google_api_calls = $wpdb->get_var("SELECT COUNT(*) FROM $api_logs_table WHERE api_type = 'google_maps'");
        $rentcast_api_calls = $wpdb->get_var("SELECT COUNT(*) FROM $api_logs_table WHERE api_type = 'rentcast'");
        
        ?>
        <table class="form-table">
            <tbody>
                <tr>
                    <th><?php _e('Total Submissions', 'georgia-property-tax-calculator'); ?></th>
                    <td><?php echo number_format($total_submissions); ?></td>
                </tr>
                <tr>
                    <th><?php _e('Submissions Today', 'georgia-property-tax-calculator'); ?></th>
                    <td><?php echo number_format($submissions_today); ?></td>
                </tr>
                <tr>
                    <th><?php _e('Submissions This Month', 'georgia-property-tax-calculator'); ?></th>
                    <td><?php echo number_format($submissions_this_month); ?></td>
                </tr>
                <tr>
                    <th><?php _e('Google Maps API Calls', 'georgia-property-tax-calculator'); ?></th>
                    <td><?php echo number_format($google_api_calls); ?></td>
                </tr>
                <tr>
                    <th><?php _e('RentCast API Calls', 'georgia-property-tax-calculator'); ?></th>
                    <td><?php echo number_format($rentcast_api_calls); ?></td>
                </tr>
            </tbody>
        </table>
        <?php
    }
    
    /**
     * Admin notices
     */
    public function admin_notices() {
        $screen = get_current_screen();
        if ($screen->id !== 'settings_page_gptc-settings') {
            return;
        }
        
        // Check if API keys are configured
        $google_key = get_option('gptc_google_maps_api_key', '');
        $rentcast_key = get_option('gptc_rentcast_api_key', '');
        
        if (empty($google_key) || empty($rentcast_key)) {
            ?>
            <div class="notice notice-warning">
                <p>
                    <?php _e('Please configure your API keys to enable full functionality of the Property Tax Calculator.', 'georgia-property-tax-calculator'); ?>
                </p>
            </div>
            <?php
        }
    }
    
    /**
     * Callback functions for settings sections
     */
    public function api_settings_callback() {
        echo '<p>' . __('Configure your API keys for Google Maps and RentCast services.', 'georgia-property-tax-calculator') . '</p>';
    }
    
    public function business_settings_callback() {
        echo '<p>' . __('Configure your business information for email notifications.', 'georgia-property-tax-calculator') . '</p>';
    }
    
    public function google_maps_api_key_callback() {
        // This is handled in the main settings page
    }
    
    public function rentcast_api_key_callback() {
        // This is handled in the main settings page
    }
    
    public function business_email_callback() {
        // This is handled in the main settings page
    }
    
    public function company_phone_callback() {
        // This is handled in the main settings page
    }
    
    public function company_logo_callback() {
        // This is handled in the main settings page
    }
    
    /**
     * Sanitize API key
     */
    public function sanitize_api_key($input) {
        return sanitize_text_field($input);
    }
}
