<?php
/**
 * API Integration for Georgia Property Tax Calculator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GPTC_API {
    
    /**
     * Google Maps API base URL
     */
    const GOOGLE_MAPS_API_URL = 'https://maps.googleapis.com/maps/api/geocode/json';
    
    /**
     * RentCast API base URL
     */
    const RENTCAST_API_URL = 'https://api.rentcast.io/v1/avm';
    
    /**
     * Get geocoding data from Google Maps API
     */
    public static function geocode_address($address) {
        $api_key = get_option('gptc_google_maps_api_key', '');
        
        if (empty($api_key)) {
            return new WP_Error('no_api_key', __('Google Maps API key not configured.', 'georgia-property-tax-calculator'));
        }
        
        $start_time = microtime(true);
        
        $url = add_query_arg(array(
            'address' => urlencode($address . ', Georgia, USA'),
            'key' => $api_key,
            'components' => 'administrative_area:Georgia|country:US'
        ), self::GOOGLE_MAPS_API_URL);
        
        $response = wp_remote_get($url, array(
            'timeout' => 15,
            'headers' => array(
                'User-Agent' => 'Georgia Property Tax Calculator/1.0'
            )
        ));
        
        $execution_time = microtime(true) - $start_time;
        
        if (is_wp_error($response)) {
            GPTC_Database::log_api_request(
                'google_maps',
                array('address' => $address),
                array(),
                0,
                $response->get_error_message(),
                $execution_time
            );
            
            return $response;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        GPTC_Database::log_api_request(
            'google_maps',
            array('address' => $address),
            $data,
            $status_code,
            '',
            $execution_time
        );
        
        if ($status_code !== 200) {
            return new WP_Error('api_error', sprintf(__('Google Maps API error: %s', 'georgia-property-tax-calculator'), $status_code));
        }
        
        if (!isset($data['status']) || $data['status'] !== 'OK') {
            $error_message = isset($data['error_message']) ? $data['error_message'] : __('Unknown error', 'georgia-property-tax-calculator');
            return new WP_Error('geocoding_failed', sprintf(__('Geocoding failed: %s', 'georgia-property-tax-calculator'), $error_message));
        }
        
        if (empty($data['results'])) {
            return new WP_Error('no_results', __('No geocoding results found for the address.', 'georgia-property-tax-calculator'));
        }
        
        $result = $data['results'][0];
        
        // Extract county information
        $county = self::extract_county_from_geocoding($result);
        
        return array(
            'formatted_address' => $result['formatted_address'],
            'latitude' => $result['geometry']['location']['lat'],
            'longitude' => $result['geometry']['location']['lng'],
            'county' => $county,
            'place_id' => $result['place_id']
        );
    }
    
    /**
     * Extract county from geocoding result
     */
    private static function extract_county_from_geocoding($result) {
        $county = '';
        
        if (isset($result['address_components'])) {
            foreach ($result['address_components'] as $component) {
                if (in_array('administrative_area_level_2', $component['types'])) {
                    $county = $component['long_name'];
                    break;
                }
            }
        }
        
        // Clean county name
        return GPTC_County_Data::clean_county_name($county);
    }
    
    /**
     * Get property value from RentCast API
     */
    public static function get_property_value($address, $latitude = null, $longitude = null) {
        $api_key = get_option('gptc_rentcast_api_key', '');
        
        if (empty($api_key)) {
            return new WP_Error('no_api_key', __('RentCast API key not configured.', 'georgia-property-tax-calculator'));
        }
        
        $start_time = microtime(true);
        
        // Prepare request data
        $request_data = array();
        
        if (!empty($latitude) && !empty($longitude)) {
            $request_data['latitude'] = $latitude;
            $request_data['longitude'] = $longitude;
        } else {
            $request_data['address'] = $address;
        }
        
        $url = add_query_arg($request_data, self::RENTCAST_API_URL);
        
        $response = wp_remote_get($url, array(
            'timeout' => 15,
            'headers' => array(
                'X-Api-Key' => $api_key,
                'User-Agent' => 'Georgia Property Tax Calculator/1.0',
                'Accept' => 'application/json'
            )
        ));
        
        $execution_time = microtime(true) - $start_time;
        
        if (is_wp_error($response)) {
            GPTC_Database::log_api_request(
                'rentcast',
                $request_data,
                array(),
                0,
                $response->get_error_message(),
                $execution_time
            );
            
            return $response;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        GPTC_Database::log_api_request(
            'rentcast',
            $request_data,
            $data,
            $status_code,
            '',
            $execution_time
        );
        
        if ($status_code !== 200) {
            $error_message = isset($data['message']) ? $data['message'] : sprintf(__('API error: %s', 'georgia-property-tax-calculator'), $status_code);
            return new WP_Error('api_error', $error_message);
        }
        
        if (!isset($data['avm'])) {
            return new WP_Error('no_avm_data', __('No property value data available.', 'georgia-property-tax-calculator'));
        }
        
        $avm_data = $data['avm'];
        
        return array(
            'value' => isset($avm_data['value']) ? floatval($avm_data['value']) : 0,
            'confidence' => isset($avm_data['confidence']) ? $avm_data['confidence'] : 'unknown',
            'value_low' => isset($avm_data['valueLow']) ? floatval($avm_data['valueLow']) : 0,
            'value_high' => isset($avm_data['valueHigh']) ? floatval($avm_data['valueHigh']) : 0,
            'last_updated' => isset($avm_data['lastUpdated']) ? $avm_data['lastUpdated'] : '',
            'property_type' => isset($data['propertyType']) ? $data['propertyType'] : 'unknown'
        );
    }
    
    /**
     * Test Google Maps API connection
     */
    public static function test_google_maps_api() {
        $test_address = '1600 Amphitheatre Parkway, Mountain View, CA';
        
        $result = self::geocode_address($test_address);
        
        if (is_wp_error($result)) {
            return array(
                'success' => false,
                'message' => $result->get_error_message()
            );
        }
        
        return array(
            'success' => true,
            'message' => __('Google Maps API is working correctly.', 'georgia-property-tax-calculator'),
            'data' => $result
        );
    }
    
    /**
     * Test RentCast API connection
     */
    public static function test_rentcast_api() {
        $test_address = '1600 Amphitheatre Parkway, Mountain View, CA';
        
        $result = self::get_property_value($test_address);
        
        if (is_wp_error($result)) {
            return array(
                'success' => false,
                'message' => $result->get_error_message()
            );
        }
        
        return array(
            'success' => true,
            'message' => __('RentCast API is working correctly.', 'georgia-property-tax-calculator'),
            'data' => $result
        );
    }
    
    /**
     * Get comprehensive property data
     */
    public static function get_comprehensive_property_data($address) {
        $results = array(
            'address' => $address,
            'geocoding' => null,
            'property_value' => null,
            'county_data' => null,
            'errors' => array()
        );
        
        // Step 1: Geocode the address
        $geocoding_result = self::geocode_address($address);
        
        if (is_wp_error($geocoding_result)) {
            $results['errors']['geocoding'] = $geocoding_result->get_error_message();
            
            // Try to proceed with just the address for property value
            $property_value_result = self::get_property_value($address);
        } else {
            $results['geocoding'] = $geocoding_result;
            
            // Step 2: Get property value using coordinates if available
            $property_value_result = self::get_property_value(
                $address,
                $geocoding_result['latitude'],
                $geocoding_result['longitude']
            );
            
            // Step 3: Get county data
            if (!empty($geocoding_result['county'])) {
                $county_data = GPTC_County_Data::get_county_by_name($geocoding_result['county']);
                if ($county_data) {
                    $results['county_data'] = $county_data;
                } else {
                    $results['errors']['county'] = sprintf(__('County data not found for: %s', 'georgia-property-tax-calculator'), $geocoding_result['county']);
                }
            }
        }
        
        if (is_wp_error($property_value_result)) {
            $results['errors']['property_value'] = $property_value_result->get_error_message();
        } else {
            $results['property_value'] = $property_value_result;
        }
        
        return $results;
    }
    
    /**
     * Generate fallback property value
     */
    public static function generate_fallback_property_value($address) {
        // Simple fallback based on address characteristics
        $base_value = 200000; // Base value for Georgia properties
        
        // Adjust based on address keywords
        $address_lower = strtolower($address);
        
        if (strpos($address_lower, 'atlanta') !== false) {
            $base_value *= 1.5;
        } elseif (strpos($address_lower, 'savannah') !== false) {
            $base_value *= 1.3;
        } elseif (strpos($address_lower, 'augusta') !== false) {
            $base_value *= 1.2;
        }
        
        // Add some randomization to make it seem more realistic
        $variation = rand(-20, 20) / 100; // ±20%
        $final_value = $base_value * (1 + $variation);
        
        return array(
            'value' => round($final_value, -3), // Round to nearest thousand
            'confidence' => 'low',
            'value_low' => round($final_value * 0.8, -3),
            'value_high' => round($final_value * 1.2, -3),
            'last_updated' => current_time('Y-m-d'),
            'property_type' => 'estimated',
            'is_fallback' => true
        );
    }
    
    /**
     * Validate API keys
     */
    public static function validate_api_keys() {
        $results = array(
            'google_maps' => false,
            'rentcast' => false,
            'errors' => array()
        );
        
        // Test Google Maps API
        $google_test = self::test_google_maps_api();
        $results['google_maps'] = $google_test['success'];
        if (!$google_test['success']) {
            $results['errors']['google_maps'] = $google_test['message'];
        }
        
        // Test RentCast API
        $rentcast_test = self::test_rentcast_api();
        $results['rentcast'] = $rentcast_test['success'];
        if (!$rentcast_test['success']) {
            $results['errors']['rentcast'] = $rentcast_test['message'];
        }
        
        return $results;
    }
}
