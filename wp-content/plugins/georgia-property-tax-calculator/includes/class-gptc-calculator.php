<?php
/**
 * Tax Calculation Engine for Georgia Property Tax Calculator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GPTC_Calculator {
    
    /**
     * Calculate Georgia property tax
     */
    public static function calculate_property_tax($property_value, $county_data, $exemptions = array()) {
        // Validate inputs
        if (empty($property_value) || $property_value <= 0) {
            return new WP_Error('invalid_property_value', __('Property value must be greater than zero.', 'georgia-property-tax-calculator'));
        }
        
        if (empty($county_data)) {
            return new WP_Error('missing_county_data', __('County data is required for tax calculation.', 'georgia-property-tax-calculator'));
        }
        
        // Get calculation parameters
        $assessment_ratio = isset($county_data['assessment_ratio']) ? floatval($county_data['assessment_ratio']) : 40.0;
        $millage_rate = isset($county_data['millage_rate']) ? floatval($county_data['millage_rate']) : 32.5;
        
        // Calculate assessed value (Georgia uses 40% assessment ratio for residential)
        $assessed_value = $property_value * ($assessment_ratio / 100);
        
        // Calculate total exemptions
        $total_exemptions = self::calculate_total_exemptions($county_data, $exemptions);
        
        // Calculate taxable value
        $taxable_value = max(0, $assessed_value - $total_exemptions);
        
        // Calculate annual tax (millage rate is per $1,000 of taxable value)
        $annual_tax = ($taxable_value / 1000) * $millage_rate;
        
        // Prepare detailed breakdown
        $calculation_details = array(
            'property_value' => $property_value,
            'assessment_ratio' => $assessment_ratio,
            'assessed_value' => $assessed_value,
            'exemptions' => array(
                'homestead' => isset($exemptions['homestead']) ? floatval($exemptions['homestead']) : floatval($county_data['homestead_exemption']),
                'senior' => isset($exemptions['senior']) ? floatval($exemptions['senior']) : 0,
                'disabled' => isset($exemptions['disabled']) ? floatval($exemptions['disabled']) : 0,
                'total' => $total_exemptions
            ),
            'taxable_value' => $taxable_value,
            'millage_rate' => $millage_rate,
            'annual_tax' => $annual_tax,
            'monthly_tax' => $annual_tax / 12,
            'county_name' => isset($county_data['county_name']) ? $county_data['county_name'] : 'Unknown'
        );
        
        return $calculation_details;
    }
    
    /**
     * Calculate total exemptions
     */
    private static function calculate_total_exemptions($county_data, $exemptions = array()) {
        $total = 0;
        
        // Homestead exemption (most common)
        if (isset($exemptions['homestead']) && $exemptions['homestead'] > 0) {
            $total += floatval($exemptions['homestead']);
        } elseif (isset($county_data['homestead_exemption'])) {
            $total += floatval($county_data['homestead_exemption']);
        }
        
        // Senior exemption
        if (isset($exemptions['senior']) && $exemptions['senior'] > 0) {
            $total += floatval($exemptions['senior']);
        } elseif (isset($exemptions['apply_senior']) && $exemptions['apply_senior'] && isset($county_data['senior_exemption'])) {
            $total += floatval($county_data['senior_exemption']);
        }
        
        // Disabled exemption
        if (isset($exemptions['disabled']) && $exemptions['disabled'] > 0) {
            $total += floatval($exemptions['disabled']);
        } elseif (isset($exemptions['apply_disabled']) && $exemptions['apply_disabled'] && isset($county_data['disabled_exemption'])) {
            $total += floatval($county_data['disabled_exemption']);
        }
        
        return $total;
    }
    
    /**
     * Calculate tax with different scenarios
     */
    public static function calculate_tax_scenarios($property_value, $county_data) {
        $scenarios = array();
        
        // Scenario 1: No exemptions
        $no_exemptions = self::calculate_property_tax($property_value, $county_data, array('homestead' => 0));
        if (!is_wp_error($no_exemptions)) {
            $scenarios['no_exemptions'] = $no_exemptions;
        }
        
        // Scenario 2: Homestead exemption only
        $homestead_only = self::calculate_property_tax($property_value, $county_data);
        if (!is_wp_error($homestead_only)) {
            $scenarios['homestead_only'] = $homestead_only;
        }
        
        // Scenario 3: Homestead + Senior exemption
        $homestead_senior = self::calculate_property_tax($property_value, $county_data, array('apply_senior' => true));
        if (!is_wp_error($homestead_senior)) {
            $scenarios['homestead_senior'] = $homestead_senior;
        }
        
        // Scenario 4: All exemptions
        $all_exemptions = self::calculate_property_tax($property_value, $county_data, array(
            'apply_senior' => true,
            'apply_disabled' => true
        ));
        if (!is_wp_error($all_exemptions)) {
            $scenarios['all_exemptions'] = $all_exemptions;
        }
        
        return $scenarios;
    }
    
    /**
     * Compare taxes across different counties
     */
    public static function compare_counties($property_value, $county_names = array()) {
        if (empty($county_names)) {
            // Get top 10 most populous Georgia counties for comparison
            $county_names = array('Fulton', 'Gwinnett', 'DeKalb', 'Cobb', 'Clayton', 'Cherokee', 'Henry', 'Forsyth', 'Hall', 'Houston');
        }
        
        $comparisons = array();
        
        foreach ($county_names as $county_name) {
            $county_data = GPTC_County_Data::get_county_by_name($county_name);
            
            if ($county_data) {
                $calculation = self::calculate_property_tax($property_value, $county_data);
                
                if (!is_wp_error($calculation)) {
                    $comparisons[$county_name] = $calculation;
                }
            }
        }
        
        // Sort by annual tax amount
        uasort($comparisons, function($a, $b) {
            return $a['annual_tax'] <=> $b['annual_tax'];
        });
        
        return $comparisons;
    }
    
    /**
     * Calculate tax savings from exemptions
     */
    public static function calculate_exemption_savings($property_value, $county_data) {
        $base_calculation = self::calculate_property_tax($property_value, $county_data, array('homestead' => 0));
        $with_exemptions = self::calculate_property_tax($property_value, $county_data);
        
        if (is_wp_error($base_calculation) || is_wp_error($with_exemptions)) {
            return new WP_Error('calculation_error', __('Unable to calculate exemption savings.', 'georgia-property-tax-calculator'));
        }
        
        $annual_savings = $base_calculation['annual_tax'] - $with_exemptions['annual_tax'];
        $monthly_savings = $annual_savings / 12;
        
        return array(
            'annual_savings' => $annual_savings,
            'monthly_savings' => $monthly_savings,
            'percentage_savings' => ($base_calculation['annual_tax'] > 0) ? ($annual_savings / $base_calculation['annual_tax']) * 100 : 0,
            'base_tax' => $base_calculation['annual_tax'],
            'tax_with_exemptions' => $with_exemptions['annual_tax']
        );
    }
    
    /**
     * Estimate tax impact of property value changes
     */
    public static function calculate_value_impact($current_value, $county_data, $percentage_changes = array()) {
        if (empty($percentage_changes)) {
            $percentage_changes = array(-20, -10, -5, 5, 10, 20); // Default percentage changes
        }
        
        $impacts = array();
        $base_calculation = self::calculate_property_tax($current_value, $county_data);
        
        if (is_wp_error($base_calculation)) {
            return $base_calculation;
        }
        
        foreach ($percentage_changes as $change) {
            $new_value = $current_value * (1 + ($change / 100));
            $new_calculation = self::calculate_property_tax($new_value, $county_data);
            
            if (!is_wp_error($new_calculation)) {
                $tax_difference = $new_calculation['annual_tax'] - $base_calculation['annual_tax'];
                
                $impacts[] = array(
                    'percentage_change' => $change,
                    'new_value' => $new_value,
                    'new_annual_tax' => $new_calculation['annual_tax'],
                    'tax_difference' => $tax_difference,
                    'monthly_difference' => $tax_difference / 12
                );
            }
        }
        
        return array(
            'base_value' => $current_value,
            'base_annual_tax' => $base_calculation['annual_tax'],
            'impacts' => $impacts
        );
    }
    
    /**
     * Validate calculation inputs
     */
    public static function validate_inputs($property_value, $county_data, $exemptions = array()) {
        $errors = array();
        
        // Validate property value
        if (empty($property_value) || !is_numeric($property_value) || $property_value <= 0) {
            $errors[] = __('Property value must be a positive number.', 'georgia-property-tax-calculator');
        } elseif ($property_value > 50000000) { // $50M limit
            $errors[] = __('Property value seems unusually high. Please verify.', 'georgia-property-tax-calculator');
        } elseif ($property_value < 10000) { // $10K minimum
            $errors[] = __('Property value seems unusually low. Please verify.', 'georgia-property-tax-calculator');
        }
        
        // Validate county data
        if (empty($county_data)) {
            $errors[] = __('County information is required.', 'georgia-property-tax-calculator');
        } else {
            if (!isset($county_data['millage_rate']) || $county_data['millage_rate'] <= 0) {
                $errors[] = __('Valid millage rate is required.', 'georgia-property-tax-calculator');
            }
            
            if (!isset($county_data['assessment_ratio']) || $county_data['assessment_ratio'] <= 0 || $county_data['assessment_ratio'] > 100) {
                $errors[] = __('Valid assessment ratio is required.', 'georgia-property-tax-calculator');
            }
        }
        
        // Validate exemptions
        if (!empty($exemptions)) {
            foreach ($exemptions as $key => $value) {
                if (is_numeric($value) && $value < 0) {
                    $errors[] = sprintf(__('Exemption %s cannot be negative.', 'georgia-property-tax-calculator'), $key);
                }
            }
        }
        
        return $errors;
    }
    
    /**
     * Format calculation results for display
     */
    public static function format_calculation_results($calculation) {
        if (is_wp_error($calculation)) {
            return $calculation;
        }
        
        return array(
            'property_value_formatted' => '$' . number_format($calculation['property_value'], 0),
            'assessed_value_formatted' => '$' . number_format($calculation['assessed_value'], 0),
            'taxable_value_formatted' => '$' . number_format($calculation['taxable_value'], 0),
            'total_exemptions_formatted' => '$' . number_format($calculation['exemptions']['total'], 0),
            'annual_tax_formatted' => '$' . number_format($calculation['annual_tax'], 2),
            'monthly_tax_formatted' => '$' . number_format($calculation['monthly_tax'], 2),
            'millage_rate_formatted' => number_format($calculation['millage_rate'], 3) . ' mills',
            'assessment_ratio_formatted' => number_format($calculation['assessment_ratio'], 1) . '%',
            'raw_data' => $calculation
        );
    }
    
    /**
     * Get calculation summary for emails
     */
    public static function get_calculation_summary($calculation) {
        if (is_wp_error($calculation)) {
            return '';
        }
        
        $formatted = self::format_calculation_results($calculation);
        
        $summary = sprintf(
            __("Property Tax Calculation Summary:\n\nProperty Value: %s\nCounty: %s\nAssessed Value: %s (%s assessment ratio)\nExemptions: %s\nTaxable Value: %s\nMillage Rate: %s\n\nEstimated Annual Tax: %s\nEstimated Monthly Tax: %s", 'georgia-property-tax-calculator'),
            $formatted['property_value_formatted'],
            $calculation['county_name'],
            $formatted['assessed_value_formatted'],
            $formatted['assessment_ratio_formatted'],
            $formatted['total_exemptions_formatted'],
            $formatted['taxable_value_formatted'],
            $formatted['millage_rate_formatted'],
            $formatted['annual_tax_formatted'],
            $formatted['monthly_tax_formatted']
        );
        
        return $summary;
    }
}
