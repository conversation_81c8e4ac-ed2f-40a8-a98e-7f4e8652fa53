<?php
/**
 * County Data Management for Georgia Property Tax Calculator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GPTC_County_Data {
    
    /**
     * Single instance
     */
    private static $instance = null;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Hook for admin actions if needed
    }
    
    /**
     * Get county data by name
     */
    public static function get_county_by_name($county_name) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'gptc_counties';
        
        // Clean county name (remove "County" suffix if present)
        $clean_name = self::clean_county_name($county_name);
        
        $county = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table WHERE county_name = %s OR county_name = %s",
                $clean_name,
                $county_name
            ),
            ARRAY_A
        );
        
        return $county;
    }
    
    /**
     * Get all counties
     */
    public static function get_all_counties() {
        global $wpdb;
        
        $table = $wpdb->prefix . 'gptc_counties';
        
        return $wpdb->get_results(
            "SELECT * FROM $table ORDER BY county_name ASC",
            ARRAY_A
        );
    }
    
    /**
     * Search counties by partial name
     */
    public static function search_counties($search_term) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'gptc_counties';
        
        return $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table WHERE county_name LIKE %s ORDER BY county_name ASC",
                '%' . $wpdb->esc_like($search_term) . '%'
            ),
            ARRAY_A
        );
    }
    
    /**
     * Get county by coordinates (for geocoding results)
     */
    public static function get_county_by_coordinates($lat, $lng) {
        // This would typically use a more sophisticated geographic lookup
        // For now, we'll use the Google Maps API result or fallback to default
        return null;
    }
    
    /**
     * Clean county name
     */
    public static function clean_county_name($county_name) {
        // Remove common suffixes
        $county_name = trim($county_name);
        $county_name = preg_replace('/\s+(County|Co\.?)$/i', '', $county_name);
        
        // Handle special cases
        $special_cases = array(
            'DeKalb' => 'DeKalb',
            'De Kalb' => 'DeKalb',
            'Jeff Davis' => 'Jeff Davis',
            'Ben Hill' => 'Ben Hill'
        );
        
        foreach ($special_cases as $variant => $correct) {
            if (strcasecmp($county_name, $variant) === 0) {
                return $correct;
            }
        }
        
        return $county_name;
    }
    
    /**
     * Get default county data (fallback)
     */
    public static function get_default_county_data() {
        return array(
            'county_name' => 'Fulton',
            'county_code' => 'FUL',
            'millage_rate' => 32.5,
            'homestead_exemption' => 2000,
            'senior_exemption' => 4000,
            'disabled_exemption' => 0,
            'assessment_ratio' => 40
        );
    }
    
    /**
     * Update county data
     */
    public static function update_county_data($county_id, $data) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'gptc_counties';
        
        $update_data = array();
        $update_format = array();
        
        // Validate and prepare data
        if (isset($data['millage_rate'])) {
            $update_data['millage_rate'] = floatval($data['millage_rate']);
            $update_format[] = '%f';
        }
        
        if (isset($data['homestead_exemption'])) {
            $update_data['homestead_exemption'] = floatval($data['homestead_exemption']);
            $update_format[] = '%f';
        }
        
        if (isset($data['senior_exemption'])) {
            $update_data['senior_exemption'] = floatval($data['senior_exemption']);
            $update_format[] = '%f';
        }
        
        if (isset($data['disabled_exemption'])) {
            $update_data['disabled_exemption'] = floatval($data['disabled_exemption']);
            $update_format[] = '%f';
        }
        
        if (isset($data['assessment_ratio'])) {
            $update_data['assessment_ratio'] = floatval($data['assessment_ratio']);
            $update_format[] = '%f';
        }
        
        if (empty($update_data)) {
            return false;
        }
        
        return $wpdb->update(
            $table,
            $update_data,
            array('id' => intval($county_id)),
            $update_format,
            array('%d')
        );
    }
    
    /**
     * Get county statistics
     */
    public static function get_county_statistics() {
        global $wpdb;
        
        $counties_table = $wpdb->prefix . 'gptc_counties';
        $submissions_table = $wpdb->prefix . 'gptc_submissions';
        
        // Get county usage statistics
        $stats = $wpdb->get_results(
            "SELECT 
                c.county_name,
                c.millage_rate,
                COUNT(s.id) as submission_count,
                AVG(s.property_value) as avg_property_value,
                AVG(s.estimated_tax) as avg_estimated_tax
            FROM $counties_table c
            LEFT JOIN $submissions_table s ON c.county_name = s.county
            GROUP BY c.id, c.county_name, c.millage_rate
            ORDER BY submission_count DESC, c.county_name ASC",
            ARRAY_A
        );
        
        return $stats;
    }
    
    /**
     * Validate county data
     */
    public static function validate_county_data($data) {
        $errors = array();
        
        // Validate millage rate
        if (isset($data['millage_rate'])) {
            $millage_rate = floatval($data['millage_rate']);
            if ($millage_rate < 0 || $millage_rate > 100) {
                $errors[] = __('Millage rate must be between 0 and 100.', 'georgia-property-tax-calculator');
            }
        }
        
        // Validate exemptions
        $exemption_fields = array('homestead_exemption', 'senior_exemption', 'disabled_exemption');
        foreach ($exemption_fields as $field) {
            if (isset($data[$field])) {
                $value = floatval($data[$field]);
                if ($value < 0) {
                    $errors[] = sprintf(__('%s cannot be negative.', 'georgia-property-tax-calculator'), ucwords(str_replace('_', ' ', $field)));
                }
            }
        }
        
        // Validate assessment ratio
        if (isset($data['assessment_ratio'])) {
            $ratio = floatval($data['assessment_ratio']);
            if ($ratio < 0 || $ratio > 100) {
                $errors[] = __('Assessment ratio must be between 0 and 100.', 'georgia-property-tax-calculator');
            }
        }
        
        return $errors;
    }
    
    /**
     * Import county data from CSV
     */
    public static function import_from_csv($file_path) {
        if (!file_exists($file_path)) {
            return new WP_Error('file_not_found', __('CSV file not found.', 'georgia-property-tax-calculator'));
        }
        
        $handle = fopen($file_path, 'r');
        if (!$handle) {
            return new WP_Error('file_read_error', __('Could not read CSV file.', 'georgia-property-tax-calculator'));
        }
        
        global $wpdb;
        $table = $wpdb->prefix . 'gptc_counties';
        
        $imported = 0;
        $errors = array();
        
        // Skip header row
        fgetcsv($handle);
        
        while (($data = fgetcsv($handle)) !== false) {
            if (count($data) < 6) {
                continue;
            }
            
            $county_data = array(
                'county_name' => sanitize_text_field($data[0]),
                'county_code' => sanitize_text_field($data[1]),
                'millage_rate' => floatval($data[2]),
                'homestead_exemption' => floatval($data[3]),
                'senior_exemption' => floatval($data[4]),
                'assessment_ratio' => floatval($data[5])
            );
            
            // Validate data
            $validation_errors = self::validate_county_data($county_data);
            if (!empty($validation_errors)) {
                $errors[] = sprintf(__('Row %d: %s', 'georgia-property-tax-calculator'), $imported + 1, implode(', ', $validation_errors));
                continue;
            }
            
            // Check if county already exists
            $existing = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT id FROM $table WHERE county_name = %s",
                    $county_data['county_name']
                )
            );
            
            if ($existing) {
                // Update existing county
                $wpdb->update(
                    $table,
                    $county_data,
                    array('id' => $existing),
                    array('%s', '%s', '%f', '%f', '%f', '%f'),
                    array('%d')
                );
            } else {
                // Insert new county
                $wpdb->insert(
                    $table,
                    $county_data,
                    array('%s', '%s', '%f', '%f', '%f', '%f')
                );
            }
            
            $imported++;
        }
        
        fclose($handle);
        
        if (!empty($errors)) {
            return new WP_Error('import_errors', sprintf(__('Imported %d counties with errors: %s', 'georgia-property-tax-calculator'), $imported, implode('; ', $errors)));
        }
        
        return $imported;
    }
    
    /**
     * Export county data to CSV
     */
    public static function export_to_csv() {
        $counties = self::get_all_counties();
        
        $filename = 'georgia-counties-' . date('Y-m-d') . '.csv';
        $filepath = wp_upload_dir()['path'] . '/' . $filename;
        
        $handle = fopen($filepath, 'w');
        if (!$handle) {
            return new WP_Error('file_write_error', __('Could not create CSV file.', 'georgia-property-tax-calculator'));
        }
        
        // Write header
        fputcsv($handle, array(
            'County Name',
            'County Code',
            'Millage Rate',
            'Homestead Exemption',
            'Senior Exemption',
            'Disabled Exemption',
            'Assessment Ratio'
        ));
        
        // Write data
        foreach ($counties as $county) {
            fputcsv($handle, array(
                $county['county_name'],
                $county['county_code'],
                $county['millage_rate'],
                $county['homestead_exemption'],
                $county['senior_exemption'],
                $county['disabled_exemption'],
                $county['assessment_ratio']
            ));
        }
        
        fclose($handle);
        
        return array(
            'filepath' => $filepath,
            'filename' => $filename,
            'url' => wp_upload_dir()['url'] . '/' . $filename
        );
    }
    
    /**
     * Get county name variations for better matching
     */
    public static function get_county_name_variations($county_name) {
        $variations = array();
        $clean_name = self::clean_county_name($county_name);
        
        // Add original name
        $variations[] = $county_name;
        
        // Add clean name
        if ($clean_name !== $county_name) {
            $variations[] = $clean_name;
        }
        
        // Add with "County" suffix
        $variations[] = $clean_name . ' County';
        
        // Add common variations
        $common_variations = array(
            'DeKalb' => array('De Kalb', 'Dekalb'),
            'Jeff Davis' => array('Jefferson Davis'),
            'Ben Hill' => array('Benhill')
        );
        
        if (isset($common_variations[$clean_name])) {
            $variations = array_merge($variations, $common_variations[$clean_name]);
        }
        
        return array_unique($variations);
    }
}
