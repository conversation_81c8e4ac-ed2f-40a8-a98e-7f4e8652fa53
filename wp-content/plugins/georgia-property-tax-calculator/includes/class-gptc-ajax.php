<?php
/**
 * AJAX functionality for Georgia Property Tax Calculator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GPTC_Ajax {
    
    /**
     * Single instance
     */
    private static $instance = null;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // AJAX actions for logged in and non-logged in users
        add_action('wp_ajax_gptc_calculate_tax', array($this, 'handle_calculate_tax'));
        add_action('wp_ajax_nopriv_gptc_calculate_tax', array($this, 'handle_calculate_tax'));
        
        add_action('wp_ajax_gptc_test_api', array($this, 'handle_test_api'));
        
        add_action('wp_ajax_gptc_send_test_email', array($this, 'handle_test_email'));
    }
    
    /**
     * Handle tax calculation AJAX request
     */
    public function handle_calculate_tax() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'gptc_nonce')) {
            wp_send_json_error(array(
                'message' => __('Security check failed. Please refresh the page and try again.', 'georgia-property-tax-calculator')
            ));
        }
        
        // Validate and sanitize input
        $user_data = array(
            'name' => sanitize_text_field($_POST['name'] ?? ''),
            'email' => sanitize_email($_POST['email'] ?? ''),
            'address' => sanitize_textarea_field($_POST['address'] ?? ''),
            'county' => sanitize_text_field($_POST['county'] ?? ''),
            'property_value' => floatval($_POST['property_value'] ?? 0)
        );
        
        $exemptions = array(
            'homestead' => intval($_POST['homestead'] ?? 1),
            'senior' => intval($_POST['senior'] ?? 0),
            'disabled' => intval($_POST['disabled'] ?? 0)
        );
        
        $show_scenarios = sanitize_text_field($_POST['show_scenarios'] ?? 'no');
        
        // Validate required fields
        $validation_errors = $this->validate_form_data($user_data);
        if (!empty($validation_errors)) {
            wp_send_json_error(array(
                'message' => implode('<br>', $validation_errors)
            ));
        }
        
        try {
            // Get comprehensive property data
            $property_data = GPTC_API::get_comprehensive_property_data($user_data['address']);
            
            // Determine property value
            $final_property_value = $this->determine_property_value($user_data['property_value'], $property_data);
            
            // Determine county
            $county_data = $this->determine_county_data($user_data['county'], $property_data);
            
            if (!$county_data) {
                wp_send_json_error(array(
                    'message' => __('Unable to determine county information. Please select a county manually and try again.', 'georgia-property-tax-calculator')
                ));
            }
            
            // Prepare exemptions for calculation
            $calculation_exemptions = array();
            if ($exemptions['homestead']) {
                $calculation_exemptions['homestead'] = floatval($county_data['homestead_exemption']);
            }
            if ($exemptions['senior']) {
                $calculation_exemptions['apply_senior'] = true;
            }
            if ($exemptions['disabled']) {
                $calculation_exemptions['apply_disabled'] = true;
            }
            
            // Calculate tax
            $calculation = GPTC_Calculator::calculate_property_tax(
                $final_property_value,
                $county_data,
                $calculation_exemptions
            );
            
            if (is_wp_error($calculation)) {
                wp_send_json_error(array(
                    'message' => $calculation->get_error_message()
                ));
            }
            
            // Save submission to database
            $submission_data = array_merge($user_data, array(
                'county' => $county_data['county_name'],
                'property_value' => $final_property_value,
                'assessed_value' => $calculation['assessed_value'],
                'exemptions' => $calculation['exemptions']['total'],
                'millage_rate' => $calculation['millage_rate'],
                'estimated_tax' => $calculation['annual_tax']
            ));
            
            $submission_id = GPTC_Database::save_submission($submission_data);
            
            // Send emails
            $email_results = GPTC_Email::send_calculation_emails($user_data, $calculation, $property_data);
            
            // Prepare response
            $response_data = array(
                'html' => GPTC_Shortcode::render_results($calculation, $property_data),
                'calculation' => $calculation,
                'submission_id' => $submission_id,
                'emails_sent' => $email_results
            );
            
            // Add scenarios if requested
            if ($show_scenarios === 'yes') {
                $scenarios = GPTC_Calculator::calculate_tax_scenarios($final_property_value, $county_data);
                $response_data['scenarios_html'] = GPTC_Shortcode::render_scenarios($scenarios);
            }
            
            wp_send_json_success($response_data);
            
        } catch (Exception $e) {
            error_log('GPTC Calculation Error: ' . $e->getMessage());
            wp_send_json_error(array(
                'message' => __('An unexpected error occurred. Please try again or contact support.', 'georgia-property-tax-calculator')
            ));
        }
    }
    
    /**
     * Handle API test AJAX request
     */
    public function handle_test_api() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('Insufficient permissions.', 'georgia-property-tax-calculator')
            ));
        }
        
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'gptc_test_api')) {
            wp_send_json_error(array(
                'message' => __('Security check failed.', 'georgia-property-tax-calculator')
            ));
        }
        
        $api_type = sanitize_text_field($_POST['api_type'] ?? '');
        
        switch ($api_type) {
            case 'google_maps':
                $result = GPTC_API::test_google_maps_api();
                break;
                
            case 'rentcast':
                $result = GPTC_API::test_rentcast_api();
                break;
                
            default:
                wp_send_json_error(array(
                    'message' => __('Invalid API type.', 'georgia-property-tax-calculator')
                ));
        }
        
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }
    
    /**
     * Handle test email AJAX request
     */
    public function handle_test_email() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('Insufficient permissions.', 'georgia-property-tax-calculator')
            ));
        }
        
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'gptc_test_email')) {
            wp_send_json_error(array(
                'message' => __('Security check failed.', 'georgia-property-tax-calculator')
            ));
        }
        
        $test_email = sanitize_email($_POST['email'] ?? '');
        
        if (empty($test_email)) {
            wp_send_json_error(array(
                'message' => __('Please provide a valid email address.', 'georgia-property-tax-calculator')
            ));
        }
        
        $result = GPTC_Email::send_test_email($test_email);
        
        if ($result) {
            wp_send_json_success(array(
                'message' => sprintf(__('Test email sent successfully to %s', 'georgia-property-tax-calculator'), $test_email)
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to send test email. Please check your email configuration.', 'georgia-property-tax-calculator')
            ));
        }
    }
    
    /**
     * Validate form data
     */
    private function validate_form_data($user_data) {
        $errors = array();
        
        // Validate name
        if (empty($user_data['name'])) {
            $errors[] = __('Name is required.', 'georgia-property-tax-calculator');
        } elseif (strlen($user_data['name']) < 2) {
            $errors[] = __('Name must be at least 2 characters long.', 'georgia-property-tax-calculator');
        }
        
        // Validate email
        if (empty($user_data['email'])) {
            $errors[] = __('Email address is required.', 'georgia-property-tax-calculator');
        } elseif (!is_email($user_data['email'])) {
            $errors[] = __('Please enter a valid email address.', 'georgia-property-tax-calculator');
        }
        
        // Validate address
        if (empty($user_data['address'])) {
            $errors[] = __('Property address is required.', 'georgia-property-tax-calculator');
        } elseif (strlen($user_data['address']) < 10) {
            $errors[] = __('Please enter a complete property address.', 'georgia-property-tax-calculator');
        }
        
        // Validate property value if provided
        if (!empty($user_data['property_value'])) {
            if ($user_data['property_value'] < 10000) {
                $errors[] = __('Property value seems too low. Please verify.', 'georgia-property-tax-calculator');
            } elseif ($user_data['property_value'] > 50000000) {
                $errors[] = __('Property value seems too high. Please verify.', 'georgia-property-tax-calculator');
            }
        }
        
        return $errors;
    }
    
    /**
     * Determine final property value to use
     */
    private function determine_property_value($user_provided_value, $property_data) {
        // If user provided a value, use it
        if (!empty($user_provided_value) && $user_provided_value > 0) {
            return $user_provided_value;
        }
        
        // Try to use API value
        if (isset($property_data['property_value']['value']) && $property_data['property_value']['value'] > 0) {
            return $property_data['property_value']['value'];
        }
        
        // Generate fallback value
        $fallback = GPTC_API::generate_fallback_property_value($property_data['address'] ?? '');
        return $fallback['value'];
    }
    
    /**
     * Determine county data to use
     */
    private function determine_county_data($user_selected_county, $property_data) {
        // If user selected a county, use it
        if (!empty($user_selected_county)) {
            return GPTC_County_Data::get_county_by_name($user_selected_county);
        }
        
        // Try to use geocoded county
        if (isset($property_data['county_data'])) {
            return $property_data['county_data'];
        }
        
        // Try to get county from geocoding result
        if (isset($property_data['geocoding']['county'])) {
            return GPTC_County_Data::get_county_by_name($property_data['geocoding']['county']);
        }
        
        // Use default county (Fulton)
        return GPTC_County_Data::get_default_county_data();
    }
}
